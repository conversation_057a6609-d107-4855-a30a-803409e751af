<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>鑫淼·翼企办销售流程导航图</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <link href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css" rel="stylesheet">
  
  <!-- 配置Tailwind -->
  <script>
    tailwind.config = {
      theme: {
        extend: {
          colors: {
            primary: '#165DFF',
            secondary: '#0FC6C2',
            success: '#00B42A',
            warning: '#FF7D00',
            danger: '#F53F3F',
            info: '#86909C',
            light: '#F2F3F5',
            dark: '#1D2129',
          },
          fontFamily: {
            inter: ['Inter', 'system-ui', 'sans-serif'],
          },
          boxShadow: {
            'card': '0 4px 20px rgba(0, 0, 0, 0.08)',
            'card-hover': '0 8px 30px rgba(0, 0, 0, 0.12)',
          }
        },
      }
    }
  </script>
  
  <style type="text/tailwindcss">
    @layer utilities {
      .content-auto {
        content-visibility: auto;
      }
      .text-shadow {
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }
      .transition-transform-opacity {
        transition-property: transform, opacity;
        transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
        transition-duration: 300ms;
      }
      .scale-hover {
        @apply hover:scale-105 transition-all duration-300;
      }
      .stage-card {
        @apply bg-white rounded-xl shadow-card p-6 scale-hover;
      }
      .process-line {
        @apply h-1 bg-primary/30 relative;
      }
      .process-line::after {
        content: '';
        @apply absolute top-0 left-0 h-full bg-primary transition-all duration-1000 ease-out;
      }
      .process-line-complete::after {
        @apply w-full;
      }
      .process-line-half::after {
        @apply w-1/2;
      }
      .step-active {
        @apply border-primary bg-primary/5;
      }
      .step-completed {
        @apply border-success bg-success/5;
      }
      .step-icon {
        @apply w-10 h-10 rounded-full flex items-center justify-center text-lg font-bold transition-all duration-300;
      }
      .step-active .step-icon {
        @apply bg-primary text-white border-primary;
      }
      .step-completed .step-icon {
        @apply bg-success text-white border-success;
      }
      .step-inactive .step-icon {
        @apply bg-gray-100 text-gray-400 border-gray-200;
      }

      /* 响应式布局优化 */
      @media (max-width: 768px) {
        .grid-cols-1.md\\:grid-cols-2.lg\\:grid-cols-3 {
          grid-template-columns: repeat(1, minmax(0, 1fr));
        }
        .grid-cols-1.md\\:grid-cols-2.lg\\:grid-cols-4 {
          grid-template-columns: repeat(1, minmax(0, 1fr));
        }
        .floating-nav {
          display: none !important;
        }
      }

      @media (min-width: 768px) and (max-width: 1024px) {
        .grid-cols-1.md\\:grid-cols-2.lg\\:grid-cols-3 {
          grid-template-columns: repeat(2, minmax(0, 1fr));
        }
        .grid-cols-1.md\\:grid-cols-2.lg\\:grid-cols-4 {
          grid-template-columns: repeat(2, minmax(0, 1fr));
        }
      }

      /* 防止元素重叠 */
      .z-safe {
        z-index: 10;
      }

      .z-modal {
        z-index: 50;
      }

      .z-floating {
        z-index: 30;
      }

      /* 自定义动画 */
      @keyframes fadeInUp {
        from {
          opacity: 0;
          transform: translateY(30px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      @keyframes pulse {
        0%, 100% {
          transform: scale(1);
        }
        50% {
          transform: scale(1.05);
        }
      }

      .animate-fade-in-up {
        animation: fadeInUp 0.6s ease-out;
      }

      .animate-pulse-slow {
        animation: pulse 2s infinite;
      }

      /* 搜索结果高亮 */
      .search-highlight {
        background-color: #fef3c7;
        padding: 2px 4px;
        border-radius: 4px;
      }

      /* 进度跟踪器样式 */
      .progress-stage {
        padding: 1rem;
        border-radius: 0.5rem;
        border: 2px solid transparent;
        transition: all 0.3s ease;
      }

      .progress-stage.completed {
        background-color: rgba(0, 180, 42, 0.1);
        border-color: #00B42A;
      }

      /* 浮动导航样式 */
      .nav-dot {
        transition: all 0.3s ease;
        cursor: pointer;
      }

      .nav-dot:hover {
        transform: scale(1.25);
      }

      .nav-dot.active {
        background-color: #165DFF;
        transform: scale(1.25);
      }

      /* 模态框动画 */
      .modal-enter {
        animation: modalEnter 0.3s ease-out;
      }

      @keyframes modalEnter {
        from {
          opacity: 0;
          transform: scale(0.9) translateY(-20px);
        }
        to {
          opacity: 1;
          transform: scale(1) translateY(0);
        }
      }

      /* 响应式优化 */
      @media (max-width: 768px) {
        .stage-tab {
          font-size: 0.875rem;
          padding: 0.5rem 1rem;
        }

        .floating-nav {
          display: none;
        }
      }

      /* 打印样式 */
      @media print {
        .no-print {
          display: none !important;
        }

        .stage-content {
          display: block !important;
        }

        .hidden {
          display: block !important;
        }
      }
    }
  </style>
</head>
<body class="bg-gray-50 font-inter text-dark">
  <!-- 顶部导航 -->
  <header class="bg-white shadow-sm sticky top-0 z-50">
    <div class="container mx-auto px-4 py-4 flex items-center justify-between">
      <div class="flex items-center space-x-3">
        <div class="w-10 h-10 rounded-lg overflow-hidden bg-white shadow-sm">
          <img src="https://mmbiz.qpic.cn/mmbiz_png/Jq3LZAUQiaSbx5VFxNJTIRy8YXsbtP71kcFl7iagCx7qLICSlE93CW4H4iaOfTiaKiaBFFEZ6NKtnLibE5T71nf55iarw/640?wx_fmt=png&from=appmsg" alt="鑫淼·翼企办" class="w-full h-full object-contain">
        </div>
        <h1 class="text-xl font-bold text-dark">鑫淼·翼企办<span class="text-primary">销售流程导航图</span></h1>
      </div>
      <nav class="hidden md:flex space-x-8">
        <a href="#overview" class="text-dark hover:text-primary transition-colors">流程概览</a>
        <a href="#stages" class="text-dark hover:text-primary transition-colors">详细阶段</a>
        <a href="#resources" class="text-dark hover:text-primary transition-colors">销售资源</a>
        <a href="#contact" class="text-dark hover:text-primary transition-colors">联系我们</a>
      </nav>

      <div class="flex items-center space-x-4">
        <!-- 搜索按钮 -->
        <button id="search-btn" class="hidden md:block text-dark hover:text-primary transition-colors">
          <i class="fa fa-search text-xl"></i>
        </button>

        <!-- 进度跟踪按钮 -->
        <button id="progress-tracker-btn" class="hidden md:block text-dark hover:text-primary transition-colors">
          <i class="fa fa-tasks text-xl"></i>
        </button>

        <!-- 话术训练按钮 -->
        <button id="training-btn" class="hidden md:block text-dark hover:text-primary transition-colors">
          <i class="fa fa-graduation-cap text-xl"></i>
        </button>

        <!-- 数据分析按钮 -->
        <button id="analytics-btn" class="hidden md:block text-dark hover:text-primary transition-colors">
          <i class="fa fa-bar-chart text-xl"></i>
        </button>

        <!-- 技能评估按钮 -->
        <button id="assessment-btn" class="hidden md:block text-dark hover:text-primary transition-colors">
          <i class="fa fa-star text-xl"></i>
        </button>

        <!-- 帮助按钮 -->
        <button id="help-btn" class="text-dark hover:text-primary transition-colors">
          <i class="fa fa-question-circle text-xl"></i>
        </button>

        <button id="mobile-menu-btn" class="md:hidden text-dark text-xl">
          <i class="fa fa-bars"></i>
        </button>
      </div>
    </div>

    <!-- 移动端菜单 -->
    <div id="mobile-menu" class="hidden md:hidden bg-white border-t border-gray-200">
      <div class="container mx-auto px-4 py-4">
        <nav class="flex flex-col space-y-4">
          <a href="#overview" class="text-dark hover:text-primary transition-colors py-2">流程概览</a>
          <a href="#stages" class="text-dark hover:text-primary transition-colors py-2">详细阶段</a>
          <a href="#resources" class="text-dark hover:text-primary transition-colors py-2">销售资源</a>
          <a href="#contact" class="text-dark hover:text-primary transition-colors py-2">联系我们</a>
        </nav>
      </div>
    </div>
  </header>

  <!-- 英雄区域 -->
  <section class="bg-gradient-to-r from-primary/90 to-primary py-16 md:py-24">
    <div class="container mx-auto px-4">
      <div class="max-w-4xl mx-auto text-center">
        <h2 class="text-[clamp(2rem,5vw,3.5rem)] font-bold text-white mb-6 leading-tight text-shadow">
          高效销售流程，<br class="hidden md:block">卓越客户转化
        </h2>
        <p class="text-[clamp(1rem,2vw,1.25rem)] text-white/90 mb-8 max-w-3xl mx-auto">
          清晰的销售流程是成功的关键。我们精心设计的导航图将帮助您的团队有效跟进和转化客户，从线索获取到客户成功的完整旅程。
        </p>
        <div class="flex flex-col sm:flex-row justify-center gap-4">
          <a href="#stages" class="bg-white text-primary px-8 py-3 rounded-lg font-medium hover:bg-gray-100 transition-colors shadow-lg hover:shadow-xl transform hover:-translate-y-1 duration-300">
            探索流程 <i class="fa fa-arrow-right ml-2"></i>
          </a>
          <a href="#resources" class="bg-white/20 text-white border border-white/30 px-8 py-3 rounded-lg font-medium hover:bg-white/30 transition-colors">
            查看资源 <i class="fa fa-book ml-2"></i>
          </a>
          <button id="kpi-calculator" class="bg-secondary text-white px-8 py-3 rounded-lg font-medium hover:bg-secondary/90 transition-colors shadow-lg">
            KPI计算器 <i class="fa fa-calculator ml-2"></i>
          </button>
        </div>
      </div>
    </div>
  </section>

  <!-- 核心理念区域 -->
  <section class="py-16 bg-gradient-to-r from-gray-50 to-white">
    <div class="container mx-auto px-4">
      <div class="max-w-6xl mx-auto">
        <div class="text-center mb-12">
          <h2 class="text-[clamp(1.5rem,3vw,2.5rem)] font-bold text-dark mb-4">核心销售理念</h2>
          <p class="text-gray-600 max-w-2xl mx-auto">基于现代顾问式销售的核心理念，帮助销售团队从推销者转变为客户成功的引导者</p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
          <div class="bg-white rounded-xl shadow-card p-8 text-center hover:shadow-card-hover transition-shadow">
            <div class="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center text-primary text-2xl mx-auto mb-6">
              <i class="fa fa-lightbulb-o"></i>
            </div>
            <h3 class="text-xl font-bold text-dark mb-4">停止推销，开始引导</h3>
            <p class="text-gray-600">我们不是在卖产品，而是在为客户诊断问题并提供解决方案。销售是帮助客户成功的自然结果。</p>
          </div>

          <div class="bg-white rounded-xl shadow-card p-8 text-center hover:shadow-card-hover transition-shadow">
            <div class="w-16 h-16 bg-secondary/10 rounded-full flex items-center justify-center text-secondary text-2xl mx-auto mb-6">
              <i class="fa fa-users"></i>
            </div>
            <h3 class="text-xl font-bold text-dark mb-4">客户价值导向</h3>
            <p class="text-gray-600">以客户的业务成功为目标，通过深度需求挖掘和价值匹配，建立长期合作关系。</p>
          </div>

          <div class="bg-white rounded-xl shadow-card p-8 text-center hover:shadow-card-hover transition-shadow">
            <div class="w-16 h-16 bg-success/10 rounded-full flex items-center justify-center text-success text-2xl mx-auto mb-6">
              <i class="fa fa-line-chart"></i>
            </div>
            <h3 class="text-xl font-bold text-dark mb-4">数据驱动决策</h3>
            <p class="text-gray-600">基于BANT评分、转化率分析等数据指标，科学管理销售流程，持续优化销售效果。</p>
          </div>
        </div>

        <!-- KPI指标展示 -->
        <div class="bg-white rounded-xl shadow-card p-8">
          <h3 class="text-xl font-bold text-dark mb-6 text-center">关键绩效指标 (KPI)</h3>
          <div class="grid grid-cols-2 md:grid-cols-4 gap-6">
            <div class="text-center">
              <div class="text-3xl font-bold text-primary mb-2">60%</div>
              <div class="text-sm text-gray-600">电话-微信转化率目标</div>
            </div>
            <div class="text-center">
              <div class="text-3xl font-bold text-secondary mb-2">30%</div>
              <div class="text-sm text-gray-600">微信-演示转化率目标</div>
            </div>
            <div class="text-center">
              <div class="text-3xl font-bold text-success mb-2">25%</div>
              <div class="text-sm text-gray-600">报价-签约转化率目标</div>
            </div>
            <div class="text-center">
              <div class="text-3xl font-bold text-warning mb-2">24h</div>
              <div class="text-sm text-gray-600">A级线索响应时间</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- 流程概览 -->
  <section id="overview" class="py-16 bg-white">
    <div class="container mx-auto px-4">
      <div class="text-center mb-12">
        <h2 class="text-[clamp(1.5rem,3vw,2.5rem)] font-bold text-dark mb-4">销售流程概览</h2>
        <p class="text-gray-600 max-w-2xl mx-auto">我们的销售流程分为八个关键阶段，每个阶段都有明确的目标和行动点，确保销售团队能够高效地推进客户转化。</p>
      </div>
      
      <!-- 流程步骤时间线 -->
      <div class="relative">
        <!-- 连接线 -->
        <div class="hidden md:block absolute left-1/2 top-8 -translate-x-1/2 w-1 h-full bg-gray-200 z-0"></div>
        
        <!-- 步骤卡片 -->
        <div class="space-y-12 md:space-y-24 relative z-10">
          <!-- 步骤1 -->
          <div class="flex flex-col md:flex-row items-center">
            <div class="md:w-1/2 md:pr-12 mb-6 md:mb-0 md:text-right">
              <div class="stage-card">
                <h3 class="text-xl font-bold text-primary mb-3">线索获取阶段</h3>
                <p class="text-gray-600">通过各种渠道获取潜在客户线索，并进行评分分类，确定跟进优先级。</p>
              </div>
            </div>
            <div class="flex items-center justify-center mb-6 md:mb-0">
              <div class="step-active rounded-full border-2 w-16 h-16 flex items-center justify-center text-primary text-2xl font-bold shadow-lg">
                1
              </div>
            </div>
            <div class="md:w-1/2 md:pl-12">
              <div class="bg-gray-50 rounded-xl p-6 shadow-sm">
                <h4 class="font-bold text-dark mb-2">关键行动点</h4>
                <ul class="text-gray-600 space-y-2">
                  <li class="flex items-start">
                    <i class="fa fa-check-circle text-success mt-1 mr-2"></i>
                    <span>对所有线索进行BANT评分(预算、权限、需求、时间)</span>
                  </li>
                  <li class="flex items-start">
                    <i class="fa fa-check-circle text-success mt-1 mr-2"></i>
                    <span>根据分数将线索分为A/B/C三级</span>
                  </li>
                  <li class="flex items-start">
                    <i class="fa fa-check-circle text-success mt-1 mr-2"></i>
                    <span>A级线索24小时内联系，B级48小时内，C级纳入自动化培育</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>
          
          <!-- 步骤2 -->
          <div class="flex flex-col md:flex-row items-center">
            <div class="md:w-1/2 md:pr-12 mb-6 md:mb-0 md:text-right order-1 md:order-2">
              <div class="stage-card">
                <h3 class="text-xl font-bold text-primary mb-3">初步接触阶段</h3>
                <p class="text-gray-600">通过电话或其他渠道首次接触潜在客户，建立信任，获取联系方式，为后续跟进打下基础。</p>
              </div>
            </div>
            <div class="flex items-center justify-center mb-6 md:mb-0 order-2 md:order-1">
              <div class="step-inactive rounded-full border-2 w-16 h-16 flex items-center justify-center text-gray-400 text-2xl font-bold shadow-lg">
                2
              </div>
            </div>
            <div class="md:w-1/2 md:pl-12 order-3">
              <div class="bg-gray-50 rounded-xl p-6 shadow-sm">
                <h4 class="font-bold text-dark mb-2">关键行动点</h4>
                <ul class="text-gray-600 space-y-2">
                  <li class="flex items-start">
                    <i class="fa fa-check-circle text-success mt-1 mr-2"></i>
                    <span>准备30秒电梯pitch，快速引起兴趣</span>
                  </li>
                  <li class="flex items-start">
                    <i class="fa fa-check-circle text-success mt-1 mr-2"></i>
                    <span>首次联系目标：获取微信，不强推产品</span>
                  </li>
                  <li class="flex items-start">
                    <i class="fa fa-check-circle text-success mt-1 mr-2"></i>
                    <span>未接通策略：电话+短信+邮件多渠道尝试</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>
          
          <!-- 步骤3 -->
          <div class="flex flex-col md:flex-row items-center">
            <div class="md:w-1/2 md:pr-12 mb-6 md:mb-0 md:text-right">
              <div class="stage-card">
                <h3 class="text-xl font-bold text-primary mb-3">需求挖掘阶段</h3>
                <p class="text-gray-600">通过深入沟通，了解客户的具体需求和痛点，为客户提供针对性的解决方案。</p>
              </div>
            </div>
            <div class="flex items-center justify-center mb-6 md:mb-0">
              <div class="step-inactive rounded-full border-2 w-16 h-16 flex items-center justify-center text-gray-400 text-2xl font-bold shadow-lg">
                3
              </div>
            </div>
            <div class="md:w-1/2 md:pl-12">
              <div class="bg-gray-50 rounded-xl p-6 shadow-sm">
                <h4 class="font-bold text-dark mb-2">关键行动点</h4>
                <ul class="text-gray-600 space-y-2">
                  <li class="flex items-start">
                    <i class="fa fa-check-circle text-success mt-1 mr-2"></i>
                    <span>使用5W2H方法论挖掘需求(What/Why/Who/When/Where/How/How much)</span>
                  </li>
                  <li class="flex items-start">
                    <i class="fa fa-check-circle text-success mt-1 mr-2"></i>
                    <span>关注客户情绪变化，识别兴趣点</span>
                  </li>
                  <li class="flex items-start">
                    <i class="fa fa-check-circle text-success mt-1 mr-2"></i>
                    <span>收集具体业务数据，为后续方案做准备</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>
          
          <!-- 步骤4 -->
          <div class="flex flex-col md:flex-row items-center">
            <div class="md:w-1/2 md:pr-12 mb-6 md:mb-0 md:text-right order-1 md:order-2">
              <div class="stage-card">
                <h3 class="text-xl font-bold text-primary mb-3">邀约演示阶段</h3>
                <p class="text-gray-600">基于前期需求挖掘的结果，邀请客户参加针对性的产品演示，展示解决方案的价值和优势。</p>
              </div>
            </div>
            <div class="flex items-center justify-center mb-6 md:mb-0 order-2 md:order-1">
              <div class="step-inactive rounded-full border-2 w-16 h-16 flex items-center justify-center text-gray-400 text-2xl font-bold shadow-lg">
                4
              </div>
            </div>
            <div class="md:w-1/2 md:pl-12 order-3">
              <div class="bg-gray-50 rounded-xl p-6 shadow-sm">
                <h4 class="font-bold text-dark mb-2">关键行动点</h4>
                <ul class="text-gray-600 space-y-2">
                  <li class="flex items-start">
                    <i class="fa fa-check-circle text-success mt-1 mr-2"></i>
                    <span>基于客户痛点设计场景化演示内容</span>
                  </li>
                  <li class="flex items-start">
                    <i class="fa fa-check-circle text-success mt-1 mr-2"></i>
                    <span>使用FABE法则(功能-优势-利益-证据)包装价值</span>
                  </li>
                  <li class="flex items-start">
                    <i class="fa fa-check-circle text-success mt-1 mr-2"></i>
                    <span>邀请关键决策人参与，提高成功率</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>

          <!-- 步骤5 -->
          <div class="flex flex-col md:flex-row items-center">
            <div class="md:w-1/2 md:pr-12 mb-6 md:mb-0 md:text-right">
              <div class="stage-card">
                <h3 class="text-xl font-bold text-primary mb-3">方案演示阶段</h3>
                <p class="text-gray-600">向客户详细演示定制化解决方案，通过五步法展示价值，解答疑问，获取客户认可。</p>
              </div>
            </div>
            <div class="flex items-center justify-center mb-6 md:mb-0">
              <div class="step-inactive rounded-full border-2 w-16 h-16 flex items-center justify-center text-gray-400 text-2xl font-bold shadow-lg">
                5
              </div>
            </div>
            <div class="md:w-1/2 md:pl-12">
              <div class="bg-gray-50 rounded-xl p-6 shadow-sm">
                <h4 class="font-bold text-dark mb-2">关键行动点</h4>
                <ul class="text-gray-600 space-y-2">
                  <li class="flex items-start">
                    <i class="fa fa-check-circle text-success mt-1 mr-2"></i>
                    <span>问题确认→影响放大→解决方案呈现→价值量化→证明互动</span>
                  </li>
                  <li class="flex items-start">
                    <i class="fa fa-check-circle text-success mt-1 mr-2"></i>
                    <span>提供详细ROI计算和投资回报分析</span>
                  </li>
                  <li class="flex items-start">
                    <i class="fa fa-check-circle text-success mt-1 mr-2"></i>
                    <span>分享相关成功案例和客户证言</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>

          <!-- 步骤6 -->
          <div class="flex flex-col md:flex-row items-center">
            <div class="md:w-1/2 md:pr-12 mb-6 md:mb-0 md:text-right order-1 md:order-2">
              <div class="stage-card">
                <h3 class="text-xl font-bold text-primary mb-3">异议处理阶段</h3>
                <p class="text-gray-600">运用LAER框架系统化处理客户异议，消除购买障碍，增强客户信心和购买意愿。</p>
              </div>
            </div>
            <div class="flex items-center justify-center mb-6 md:mb-0 order-2 md:order-1">
              <div class="step-inactive rounded-full border-2 w-16 h-16 flex items-center justify-center text-gray-400 text-2xl font-bold shadow-lg">
                6
              </div>
            </div>
            <div class="md:w-1/2 md:pl-12 order-3">
              <div class="bg-gray-50 rounded-xl p-6 shadow-sm">
                <h4 class="font-bold text-dark mb-2">关键行动点</h4>
                <ul class="text-gray-600 space-y-2">
                  <li class="flex items-start">
                    <i class="fa fa-check-circle text-success mt-1 mr-2"></i>
                    <span>使用LAER框架：倾听→确认→探索→回应</span>
                  </li>
                  <li class="flex items-start">
                    <i class="fa fa-check-circle text-success mt-1 mr-2"></i>
                    <span>识别5大类异议：价格、信任、权限、时机、竞品</span>
                  </li>
                  <li class="flex items-start">
                    <i class="fa fa-check-circle text-success mt-1 mr-2"></i>
                    <span>提供风险兜底方案：试用、分期、保证等</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>

          <!-- 步骤7 -->
          <div class="flex flex-col md:flex-row items-center">
            <div class="md:w-1/2 md:pr-12 mb-6 md:mb-0 md:text-right">
              <div class="stage-card">
                <h3 class="text-xl font-bold text-primary mb-3">促成交易阶段</h3>
                <p class="text-gray-600">识别购买信号，运用成交技巧促进客户做出购买决策，完成签约交易。</p>
              </div>
            </div>
            <div class="flex items-center justify-center mb-6 md:mb-0">
              <div class="step-inactive rounded-full border-2 w-16 h-16 flex items-center justify-center text-gray-400 text-2xl font-bold shadow-lg">
                7
              </div>
            </div>
            <div class="md:w-1/2 md:pl-12">
              <div class="bg-gray-50 rounded-xl p-6 shadow-sm">
                <h4 class="font-bold text-dark mb-2">关键行动点</h4>
                <ul class="text-gray-600 space-y-2">
                  <li class="flex items-start">
                    <i class="fa fa-check-circle text-success mt-1 mr-2"></i>
                    <span>识别强烈/中等/初步三级购买信号</span>
                  </li>
                  <li class="flex items-start">
                    <i class="fa fa-check-circle text-success mt-1 mr-2"></i>
                    <span>运用假设性/选择性/紧迫感成交技巧</span>
                  </li>
                  <li class="flex items-start">
                    <i class="fa fa-check-circle text-success mt-1 mr-2"></i>
                    <span>制定让步策略：培训→支持→模块→付款→价格</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>

          <!-- 步骤8 -->
          <div class="flex flex-col md:flex-row items-center">
            <div class="md:w-1/2 md:pr-12 mb-6 md:mb-0 md:text-right order-1 md:order-2">
              <div class="stage-card">
                <h3 class="text-xl font-bold text-primary mb-3">客户成功阶段</h3>
                <p class="text-gray-600">确保客户成功实施解决方案，提供持续支持，促进客户满意度、续费和转介绍。</p>
              </div>
            </div>
            <div class="flex items-center justify-center mb-6 md:mb-0 order-2 md:order-1">
              <div class="step-inactive rounded-full border-2 w-16 h-16 flex items-center justify-center text-gray-400 text-2xl font-bold shadow-lg">
                8
              </div>
            </div>
            <div class="md:w-1/2 md:pl-12 order-3">
              <div class="bg-gray-50 rounded-xl p-6 shadow-sm">
                <h4 class="font-bold text-dark mb-2">关键行动点</h4>
                <ul class="text-gray-600 space-y-2">
                  <li class="flex items-start">
                    <i class="fa fa-check-circle text-success mt-1 mr-2"></i>
                    <span>建立7天→30天→90天客户成功检查点</span>
                  </li>
                  <li class="flex items-start">
                    <i class="fa fa-check-circle text-success mt-1 mr-2"></i>
                    <span>提供分层培训：管理层→主管→员工→IT</span>
                  </li>
                  <li class="flex items-start">
                    <i class="fa fa-check-circle text-success mt-1 mr-2"></i>
                    <span>主动挖掘扩单机会和推荐机会</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        <!-- 流程总结 -->
        <div class="mt-16 bg-gradient-to-r from-primary/5 to-secondary/5 rounded-2xl p-8">
          <div class="text-center mb-8">
            <h3 class="text-2xl font-bold text-dark mb-4">销售流程核心要点</h3>
            <p class="text-gray-600 max-w-3xl mx-auto">成功的销售不是偶然，而是遵循科学流程的必然结果。每个阶段都有其独特价值和关键作用。</p>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div class="bg-white rounded-xl p-6 text-center shadow-sm">
              <div class="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center text-primary text-xl mx-auto mb-4">
                <i class="fa fa-target"></i>
              </div>
              <h4 class="font-bold text-dark mb-2">精准定位</h4>
              <p class="text-sm text-gray-600">通过BANT评分精准识别高价值客户，提高跟进效率</p>
            </div>

            <div class="bg-white rounded-xl p-6 text-center shadow-sm">
              <div class="w-12 h-12 bg-secondary/10 rounded-full flex items-center justify-center text-secondary text-xl mx-auto mb-4">
                <i class="fa fa-comments"></i>
              </div>
              <h4 class="font-bold text-dark mb-2">深度沟通</h4>
              <p class="text-sm text-gray-600">运用SPIN提问法深入挖掘客户需求和痛点</p>
            </div>

            <div class="bg-white rounded-xl p-6 text-center shadow-sm">
              <div class="w-12 h-12 bg-success/10 rounded-full flex items-center justify-center text-success text-xl mx-auto mb-4">
                <i class="fa fa-lightbulb-o"></i>
              </div>
              <h4 class="font-bold text-dark mb-2">价值展示</h4>
              <p class="text-sm text-gray-600">通过场景化演示和ROI计算证明方案价值</p>
            </div>

            <div class="bg-white rounded-xl p-6 text-center shadow-sm">
              <div class="w-12 h-12 bg-warning/10 rounded-full flex items-center justify-center text-warning text-xl mx-auto mb-4">
                <i class="fa fa-handshake-o"></i>
              </div>
              <h4 class="font-bold text-dark mb-2">成功交付</h4>
              <p class="text-sm text-gray-600">确保客户成功实施，创造长期价值和口碑</p>
            </div>
          </div>

          <!-- 关键数据指标 -->
          <div class="mt-8 grid grid-cols-2 md:grid-cols-4 gap-4">
            <div class="text-center">
              <div class="text-2xl font-bold text-primary mb-1">60%</div>
              <div class="text-xs text-gray-600">电话-微信转化率目标</div>
            </div>
            <div class="text-center">
              <div class="text-2xl font-bold text-secondary mb-1">30%</div>
              <div class="text-xs text-gray-600">微信-演示转化率目标</div>
            </div>
            <div class="text-center">
              <div class="text-2xl font-bold text-success mb-1">25%</div>
              <div class="text-xs text-gray-600">报价-签约转化率目标</div>
            </div>
            <div class="text-center">
              <div class="text-2xl font-bold text-warning mb-1">4.5%</div>
              <div class="text-xs text-gray-600">整体转化率目标</div>
            </div>
          </div>

          <!-- 行动建议 -->
          <div class="mt-8 bg-white rounded-xl p-6">
            <h4 class="font-bold text-dark mb-4 text-center">💡 实施建议</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h5 class="font-medium text-dark mb-2">新手销售</h5>
                <ul class="text-sm text-gray-600 space-y-1">
                  <li>• 严格按照8阶段流程执行，不跳跃步骤</li>
                  <li>• 重点练习SPIN提问和LAER异议处理</li>
                  <li>• 每天复盘，记录经验和改进点</li>
                </ul>
              </div>
              <div>
                <h5 class="font-medium text-dark mb-2">资深销售</h5>
                <ul class="text-sm text-gray-600 space-y-1">
                  <li>• 根据客户特点灵活调整流程节奏</li>
                  <li>• 重点关注数据分析和流程优化</li>
                  <li>• 分享经验，帮助团队整体提升</li>
                </ul>
              </div>
            </div>
          </div>

          <!-- 快速跳转 -->
          <div class="mt-8 text-center">
            <a href="#stages" class="inline-flex items-center bg-primary text-white px-8 py-3 rounded-lg font-medium hover:bg-primary/90 transition-colors shadow-lg hover:shadow-xl transform hover:-translate-y-1 duration-300">
              查看详细阶段指导
              <i class="fa fa-arrow-down ml-2"></i>
            </a>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- 详细阶段 -->
  <section id="stages" class="py-16 bg-gray-50">
    <div class="container mx-auto px-4">
      <div class="text-center mb-12">
        <h2 class="text-[clamp(1.5rem,3vw,2.5rem)] font-bold text-dark mb-4">详细阶段导航</h2>
        <p class="text-gray-600 max-w-2xl mx-auto mb-6">探索销售流程的每个阶段，了解关键步骤、决策点和行动指南。</p>
        <a href="#overview" class="inline-flex items-center text-primary hover:text-primary/80 transition-colors">
          <i class="fa fa-arrow-up mr-2"></i>
          返回流程概览
        </a>
      </div>
      
      <!-- 阶段选择标签 -->
      <div class="flex flex-wrap justify-center gap-3 mb-10">
        <button class="stage-tab active px-6 py-3 rounded-full bg-primary text-white font-medium transition-all" data-stage="1">
          线索获取阶段
        </button>
        <button class="stage-tab px-6 py-3 rounded-full bg-gray-200 text-gray-700 font-medium transition-all hover:bg-gray-300" data-stage="2">
          初步接触阶段
        </button>
        <button class="stage-tab px-6 py-3 rounded-full bg-gray-200 text-gray-700 font-medium transition-all hover:bg-gray-300" data-stage="3">
          需求挖掘阶段
        </button>
        <button class="stage-tab px-6 py-3 rounded-full bg-gray-200 text-gray-700 font-medium transition-all hover:bg-gray-300" data-stage="4">
          邀约演示阶段
        </button>
        <button class="stage-tab px-6 py-3 rounded-full bg-gray-200 text-gray-700 font-medium transition-all hover:bg-gray-300" data-stage="5">
          方案演示阶段
        </button>
        <button class="stage-tab px-6 py-3 rounded-full bg-gray-200 text-gray-700 font-medium transition-all hover:bg-gray-300" data-stage="6">
          异议处理阶段
        </button>
        <button class="stage-tab px-6 py-3 rounded-full bg-gray-200 text-gray-700 font-medium transition-all hover:bg-gray-300" data-stage="7">
          促成交易阶段
        </button>
        <button class="stage-tab px-6 py-3 rounded-full bg-gray-200 text-gray-700 font-medium transition-all hover:bg-gray-300" data-stage="8">
          客户成功阶段
        </button>
      </div>
      
      <!-- 阶段内容 -->
      <div class="stage-content" id="stage-1-content">
        <div class="bg-white rounded-xl shadow-card p-6 md:p-8 mb-8">
          <div class="flex flex-col md:flex-row gap-8">
            <div class="md:w-1/3">
              <div class="bg-primary/5 rounded-lg p-6">
                                <h3 class="text-xl font-bold text-primary mb-4 flex items-center">
                  <i class="fa fa-lightbulb-o mr-3"></i> 核心理念
                </h3>
                <p class="text-gray-600 mb-4 font-medium italic">停止推销，开始引导。我们不是在卖产品，而是在为客户诊断问题并提供解决方案。销售是帮助客户成功的自然结果。</p>
                
                <div class="mt-6">
                  <h4 class="font-bold text-dark mb-4">阶段目标</h4>
                  <div class="bg-primary/5 rounded-lg p-4 mb-4">
                    <p class="text-primary font-bold">3分钟内建立信任，拿到微信</p>
                  </div>
                  
                  <h4 class="font-bold text-dark mb-2 mt-6">线索类型分析</h4>
                  <div class="space-y-4">
                    <div class="border-l-4 border-primary pl-4 py-2">
                      <h5 class="font-bold text-dark mb-2">官网/广告线索 (被动型)</h5>
                      <ul class="text-gray-600 space-y-2">
                        <li class="flex items-start">
                          <i class="fa fa-user text-primary mt-1 mr-2"></i>
                          <span>用户画像：主动搜索，有明确需求或痛点，认知度较高</span>
                        </li>
                        <li class="flex items-start">
                          <i class="fa fa-comments text-primary mt-1 mr-2"></i>
                          <span>沟通策略：必须体现专业性，快速切入核心问题，提供价值</span>
                        </li>
                        <li class="flex items-start">
                          <i class="fa fa-id-card text-primary mt-1 mr-2"></i>
                          <span>你的角色：专家顾问</span>
                        </li>
                      </ul>
                    </div>
                    
                    <div class="border-l-4 border-secondary pl-4 py-2">
                      <h5 class="font-bold text-dark mb-2">抖音线索 (主动型)</h5>
                      <ul class="text-gray-600 space-y-2">
                        <li class="flex items-start">
                          <i class="fa fa-user text-secondary mt-1 mr-2"></i>
                          <span>用户画像：被内容吸引，可能处于问题萌芽期，认知度浅</span>
                        </li>
                        <li class="flex items-start">
                          <i class="fa fa-comments text-secondary mt-1 mr-2"></i>
                          <span>沟通策略：需要先建立兴趣和信任，用通俗易懂的方式"翻译"产品价值，过程要更有趣、更轻松</span>
                        </li>
                        <li class="flex items-start">
                          <i class="fa fa-id-card text-secondary mt-1 mr-2"></i>
                          <span>你的角色：懂技术的朋友</span>
                        </li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="md:w-2/3">
              <h3 class="text-xl font-bold text-dark mb-6 flex items-center">
                <i class="fa fa-sitemap mr-3"></i> 流程步骤
              </h3>
              
              <!-- 流程图 -->
              <div class="bg-gray-50 rounded-lg p-6">
                <div class="flex flex-col space-y-8">
                  <!-- 步骤1 -->
                  <div class="step-card step-active rounded-lg border p-4">
                    <div class="flex items-center">
                      <div class="step-icon border-2">1</div>
                      <h4 class="font-bold text-dark ml-4">线索进入</h4>
                    </div>
                    <p class="text-gray-600 mt-3">通过市场活动、广告、社交媒体、网站表单、合作伙伴推荐等渠道获取潜在客户信息。</p>
                  </div>
                  
                  <!-- 连接线 -->
                  <div class="process-line mx-8">
                    <div class="absolute top-1/2 left-0 w-4 h-4 bg-primary rounded-full -translate-y-1/2 -translate-x-1/2"></div>
                    <div class="absolute top-1/2 right-0 w-4 h-4 bg-primary rounded-full -translate-y-1/2 translate-x-1/2"></div>
                  </div>
                  
                  <!-- 步骤2 -->
                  <div class="step-card step-active rounded-lg border p-4">
                    <div class="flex items-center">
                      <div class="step-icon border-2">2</div>
                      <h4 class="font-bold text-dark ml-4">线索评分</h4>
                    </div>
                    <p class="text-gray-600 mt-3">使用BANT方法(预算、权限、需求、时间)对线索进行评分，确定线索质量和优先级。</p>
                  </div>
                  
                  <!-- 决策点 -->
                  <div class="mx-8 flex flex-col items-center">
                    <div class="w-6 h-6 bg-primary rounded-full flex items-center justify-center text-white text-xs">
                      ?
                    </div>
                    <p class="text-center text-sm font-medium mt-2">是否为高质量线索?</p>
                  </div>
                  
                  <!-- 分支 -->
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- 分支1 -->
                    <div class="step-card step-active rounded-lg border p-4">
                      <div class="flex items-center">
                        <div class="step-icon border-2 bg-success text-white">是</div>
                      </div>
                      <div class="mt-3">
                        <h4 class="font-bold text-dark">优先分配给资深销售</h4>
                        <p class="text-gray-600 mt-2">将高质量线索优先分配给经验丰富的销售代表，确保能够快速建立联系并有效跟进。</p>
                        
                        <div class="mt-4 step-card step-active rounded-lg border p-3">
                          <h4 class="font-bold text-dark flex items-center">
                            <i class="fa fa-arrow-right text-primary mr-2"></i> 24小时内联系
                          </h4>
                          <p class="text-gray-600 mt-2">资深销售在24小时内主动联系潜在客户，进行初步沟通和需求了解。</p>
                        </div>
                      </div>
                    </div>
                    
                    <!-- 分支2 -->
                    <div class="step-card step-inactive rounded-lg border p-4">
                      <div class="flex items-center">
                        <div class="step-icon border-2 bg-warning text-white">否</div>
                      </div>
                      <div class="mt-3">
                        <h4 class="font-bold text-dark">进入培育序列</h4>
                        <p class="text-gray-600 mt-2">对于质量较低的线索，将其纳入自动化培育序列，通过定期内容推送保持联系。</p>
                        
                        <div class="mt-4 step-card step-inactive rounded-lg border p-3">
                          <h4 class="font-bold text-dark flex items-center">
                            <i class="fa fa-arrow-right text-primary mr-2"></i> 定期内容推送
                          </h4>
                          <p class="text-gray-600 mt-2">向潜在客户发送有价值的内容，如行业报告、解决方案介绍、成功案例等，建立信任和兴趣。</p>
                        </div>
                        
                        <div class="mt-4 step-card step-inactive rounded-lg border p-3">
                          <h4 class="font-bold text-dark flex items-center">
                            <i class="fa fa-arrow-right text-primary mr-2"></i> 7天后重新评估
                          </h4>
                          <p class="text-gray-600 mt-2">在7天后重新评估线索质量，看是否有足够的兴趣和需求升级为高质量线索。</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 关键行动点 -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <div class="bg-white rounded-xl shadow-card p-6 hover:shadow-card-hover transition-shadow">
            <div class="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center text-primary text-xl mb-4">
              <i class="fa fa-star"></i>
            </div>
            <h3 class="font-bold text-dark mb-3">BANT评分</h3>
            <p class="text-gray-600">对所有线索进行BANT评分(预算、权限、需求、时间)，确保对线索质量有清晰的认识。</p>
          </div>
          
          <div class="bg-white rounded-xl shadow-card p-6 hover:shadow-card-hover transition-shadow">
            <div class="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center text-primary text-xl mb-4">
              <i class="fa fa-tags"></i>
            </div>
            <h3 class="font-bold text-dark mb-3">线索分级</h3>
            <p class="text-gray-600">根据BANT评分结果，将线索分为A/B/C三级，A级为最高优先级，C级为最低优先级。</p>
          </div>
          
          <div class="bg-white rounded-xl shadow-card p-6 hover:shadow-card-hover transition-shadow">
            <div class="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center text-primary text-xl mb-4">
              <i class="fa fa-clock-o"></i>
            </div>
            <h3 class="font-bold text-dark mb-3">响应时间</h3>
            <p class="text-gray-600">根据线索级别确定响应时间，A级线索24小时内联系，B级48小时内，C级纳入自动化培育。</p>
          </div>
        </div>
        
        <!-- 工具和模板 -->
        <div class="bg-white rounded-xl shadow-card p-6">
          <h3 class="text-xl font-bold text-dark mb-6">工具和模板</h3>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="border rounded-lg p-4 flex items-start hover:bg-gray-50 transition-colors">
              <div class="text-primary text-xl mt-1 mr-4">
                <i class="fa fa-file-text-o"></i>
              </div>
              <div>
                <h4 class="font-bold text-dark">线索评分表</h4>
                <p class="text-gray-600 text-sm mt-1">用于评估线索质量的BANT评分表模板，帮助销售团队标准化线索评估流程。</p>
                <a href="#" class="text-primary text-sm mt-2 inline-block hover:underline">
                  下载模板 <i class="fa fa-download ml-1"></i>
                </a>
              </div>
            </div>
            
            <div class="border rounded-lg p-4 flex items-start hover:bg-gray-50 transition-colors">
              <div class="text-primary text-xl mt-1 mr-4">
                <i class="fa fa-file-text-o"></i>
              </div>
              <div>
                <h4 class="font-bold text-dark">首次联系话术模板</h4>
                <p class="text-gray-600 text-sm mt-1">包含专业的首次电话联系和邮件沟通的话术模板，帮助销售代表建立良好的第一印象。</p>
                <a href="#" class="text-primary text-sm mt-2 inline-block hover:underline">
                  查看模板 <i class="fa fa-external-link ml-1"></i>
                </a>
              </div>
            </div>
            
            <div class="border rounded-lg p-4 flex items-start hover:bg-gray-50 transition-colors">
              <div class="text-primary text-xl mt-1 mr-4">
                <i class="fa fa-file-text-o"></i>
              </div>
              <div>
                <h4 class="font-bold text-dark">内容培育序列规划</h4>
                <p class="text-gray-600 text-sm mt-1">为低质量线索设计的内容培育计划模板，包括内容类型、发送频率和目标。</p>
                <a href="#" class="text-primary text-sm mt-2 inline-block hover:underline">
                  下载模板 <i class="fa fa-download ml-1"></i>
                </a>
              </div>
            </div>
            
            <div class="border rounded-lg p-4 flex items-start hover:bg-gray-50 transition-colors">
              <div class="text-primary text-xl mt-1 mr-4">
                <i class="fa fa-video-camera"></i>
              </div>
              <div>
                <h4 class="font-bold text-dark">线索获取培训视频</h4>
                <p class="text-gray-600 text-sm mt-1">关于如何有效获取和筛选高质量线索的培训视频，时长约20分钟。</p>
                <a href="#" class="text-primary text-sm mt-2 inline-block hover:underline">
                  观看视频 <i class="fa fa-play-circle ml-1"></i>
                </a>
              </div>
            </div>
          </div>
        </div>

        <!-- 阶段成功指标 -->
        <div class="bg-primary/5 rounded-xl p-6 mb-8">
          <h3 class="text-lg font-bold text-dark mb-4 flex items-center">
            <i class="fa fa-trophy text-primary mr-3"></i>
            阶段成功指标
          </h3>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div class="bg-white rounded-lg p-4 text-center">
              <div class="text-2xl font-bold text-primary mb-1">A级</div>
              <div class="text-sm text-gray-600">BANT评分16-20分</div>
            </div>
            <div class="bg-white rounded-lg p-4 text-center">
              <div class="text-2xl font-bold text-secondary mb-1">24h</div>
              <div class="text-sm text-gray-600">A级线索响应时间</div>
            </div>
            <div class="bg-white rounded-lg p-4 text-center">
              <div class="text-2xl font-bold text-success mb-1">85%</div>
              <div class="text-sm text-gray-600">线索质量提升目标</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 阶段2：初步接触阶段 -->
      <div class="stage-content hidden" id="stage-2-content">
        <div class="bg-white rounded-xl shadow-card p-6 md:p-8 mb-8">
          <div class="flex flex-col md:flex-row gap-8">
            <div class="md:w-1/3">
              <div class="bg-secondary/5 rounded-lg p-6">
                <h3 class="text-xl font-bold text-secondary mb-4 flex items-center">
                  <i class="fa fa-phone mr-3"></i> 阶段目标
                </h3>
                <div class="bg-secondary/5 rounded-lg p-4 mb-4">
                  <p class="text-secondary font-bold">3分钟内建立信任，拿到微信</p>
                </div>

                <h4 class="font-bold text-dark mb-4">核心策略</h4>
                <div class="space-y-4">
                  <div class="border-l-4 border-secondary pl-4 py-2">
                    <h5 class="font-bold text-dark mb-2">电话初筛黄金话术</h5>
                    <p class="text-gray-600 text-sm">用一个"钩子"换一个微信，避免直接推销产品</p>
                  </div>

                  <div class="border-l-4 border-primary pl-4 py-2">
                    <h5 class="font-bold text-dark mb-2">开场白模板</h5>
                    <p class="text-gray-600 text-sm">您好，我是鑫淼·翼企办的[姓名]。我们注意到贵公司在[相关业务领域]有一定规模...</p>
                  </div>
                </div>
              </div>
            </div>

            <div class="md:w-2/3">
              <h3 class="text-xl font-bold text-dark mb-6 flex items-center">
                <i class="fa fa-comments mr-3"></i> 话术流程
              </h3>

              <div class="bg-gray-50 rounded-lg p-6">
                <div class="space-y-6">
                  <!-- 主动拨打流程 -->
                  <div class="bg-white rounded-lg border p-4">
                    <h4 class="font-bold text-dark mb-4 flex items-center">
                      <div class="w-8 h-8 bg-primary rounded-full flex items-center justify-center text-white text-sm mr-3">1</div>
                      主动拨打流程
                    </h4>
                    <div class="space-y-3">
                      <div class="bg-primary/5 rounded p-3">
                        <p class="text-sm"><strong>开场白：</strong>"您好，我是鑫淼·翼企办的[姓名]。我们注意到贵公司在[相关业务领域]有一定规模，我们帮助很多类似的企业解决了[客户痛点]问题。请问您是负责公司[销售/合规/管理]相关工作的吗？"</p>
                      </div>
                      <div class="bg-secondary/5 rounded p-3">
                        <p class="text-sm"><strong>行业针对性问题：</strong></p>
                        <ul class="text-sm mt-2 space-y-1">
                          <li>• 金融催收/理财保险：如何确保销售团队的通话内容合规？</li>
                          <li>• 地产家装：销售团队的客户资源如何管理？</li>
                          <li>• 教育培训：如何监控销售团队的转化效果？</li>
                        </ul>
                      </div>
                    </div>
                  </div>

                  <!-- 被动接听流程 -->
                  <div class="bg-white rounded-lg border p-4">
                    <h4 class="font-bold text-dark mb-4 flex items-center">
                      <div class="w-8 h-8 bg-secondary rounded-full flex items-center justify-center text-white text-sm mr-3">2</div>
                      被动接听流程
                    </h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div class="space-y-2">
                        <h5 class="font-medium text-dark">问候确认</h5>
                        <p class="text-sm text-gray-600">快速建立专业、亲切的沟通氛围，明确客户咨询核心诉求</p>
                      </div>
                      <div class="space-y-2">
                        <h5 class="font-medium text-dark">信息收集</h5>
                        <p class="text-sm text-gray-600">收集企业基本信息、客户状态信息、业务关键信息</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 常见异议处理 -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
          <div class="bg-white rounded-xl shadow-card p-6">
            <h3 class="font-bold text-dark mb-4 flex items-center">
              <i class="fa fa-exclamation-triangle text-warning mr-3"></i>
              常见异议处理
            </h3>
            <div class="space-y-4">
              <div class="border-l-4 border-warning pl-4">
                <h5 class="font-medium text-dark">"太贵了"</h5>
                <p class="text-sm text-gray-600 mt-1">单看价格，一天几块钱确实也是一笔投入。但我们换个算法：一个'飞单'您损失多少？一天2块钱，就能堵上一年几十万的窟窿...</p>
              </div>
              <div class="border-l-4 border-danger pl-4">
                <h5 class="font-medium text-dark">"员工会反感监控"</h5>
                <p class="text-sm text-gray-600 mt-1">我们更愿意把它定义为'赋能'。对于想好好干的员工，我们的AI话术推荐是帮他更快成单的'武器'...</p>
              </div>
            </div>
          </div>

          <div class="bg-white rounded-xl shadow-card p-6">
            <h3 class="font-bold text-dark mb-4 flex items-center">
              <i class="fa fa-phone-square text-success mr-3"></i>
              应对策略
            </h3>
            <div class="space-y-4">
              <div class="border-l-4 border-success pl-4">
                <h5 class="font-medium text-dark">客户说"没兴趣/不需要"</h5>
                <p class="text-sm text-gray-600 mt-1">完全理解。我们其实不是一个简单的软件，更像一个'业绩保险'。就当在通讯录里备个不时之需...</p>
              </div>
              <div class="border-l-4 border-info pl-4">
                <h5 class="font-medium text-dark">电话挂断/无人接听</h5>
                <p class="text-sm text-gray-600 mt-1">立即发送短信："王总您好，我是翼企办顾问小张。刚刚致电未接，看到您关注了我们的产品..."</p>
              </div>
            </div>
          </div>
        </div>

        <!-- 阶段成功指标 -->
        <div class="bg-secondary/5 rounded-xl p-6 mb-8">
          <h3 class="text-lg font-bold text-dark mb-4 flex items-center">
            <i class="fa fa-trophy text-secondary mr-3"></i>
            阶段成功指标
          </h3>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div class="bg-white rounded-lg p-4 text-center">
              <div class="text-2xl font-bold text-primary mb-1">60%</div>
              <div class="text-sm text-gray-600">电话-微信转化率目标</div>
            </div>
            <div class="bg-white rounded-lg p-4 text-center">
              <div class="text-2xl font-bold text-secondary mb-1">3min</div>
              <div class="text-sm text-gray-600">建立信任时间</div>
            </div>
            <div class="bg-white rounded-lg p-4 text-center">
              <div class="text-2xl font-bold text-success mb-1">90%</div>
              <div class="text-sm text-gray-600">微信好友激活率</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 阶段3：需求挖掘阶段 -->
      <div class="stage-content hidden" id="stage-3-content">
        <div class="bg-white rounded-xl shadow-card p-6 md:p-8 mb-8">
          <div class="flex flex-col md:flex-row gap-8">
            <div class="md:w-1/3">
              <div class="bg-success/5 rounded-lg p-6">
                <h3 class="text-xl font-bold text-success mb-4 flex items-center">
                  <i class="fa fa-search mr-3"></i> 核心方法
                </h3>
                <div class="bg-success/5 rounded-lg p-4 mb-4">
                  <p class="text-success font-bold">SPIN销售模型 + 5个问题链</p>
                </div>

                <h4 class="font-bold text-dark mb-4">SPIN模型</h4>
                <div class="space-y-3">
                  <div class="bg-white rounded p-3 border-l-4 border-success">
                    <h5 class="font-medium text-dark">S - 情境问题</h5>
                    <p class="text-xs text-gray-600">了解客户现状</p>
                  </div>
                  <div class="bg-white rounded p-3 border-l-4 border-warning">
                    <h5 class="font-medium text-dark">P - 问题识别</h5>
                    <p class="text-xs text-gray-600">发现痛点</p>
                  </div>
                  <div class="bg-white rounded p-3 border-l-4 border-danger">
                    <h5 class="font-medium text-dark">I - 影响分析</h5>
                    <p class="text-xs text-gray-600">放大问题影响</p>
                  </div>
                  <div class="bg-white rounded p-3 border-l-4 border-primary">
                    <h5 class="font-medium text-dark">N - 需求价值</h5>
                    <p class="text-xs text-gray-600">建立解决需求</p>
                  </div>
                </div>
              </div>
            </div>

            <div class="md:w-2/3">
              <h3 class="text-xl font-bold text-dark mb-6 flex items-center">
                <i class="fa fa-question-circle mr-3"></i> 5个问题链
              </h3>

              <div class="space-y-4">
                <!-- 问题链1 -->
                <div class="bg-gray-50 rounded-lg p-4">
                  <h4 class="font-bold text-dark mb-3 flex items-center">
                    <div class="w-6 h-6 bg-primary rounded-full flex items-center justify-center text-white text-xs mr-3">1</div>
                    员工行为管理
                  </h4>
                  <div class="space-y-2">
                    <p class="text-sm text-gray-700"><strong>提问逻辑：</strong>现状→痛点→影响→需求</p>
                    <div class="bg-white rounded p-3">
                      <p class="text-sm">"现在员工用私人手机/电脑办公多吗？有没有出现过聊天记录找不着、客户资源跟着员工走的情况？这对业务连续性、业绩影响大不大呀？"</p>
                    </div>
                  </div>
                </div>

                <!-- 问题链2 -->
                <div class="bg-gray-50 rounded-lg p-4">
                  <h4 class="font-bold text-dark mb-3 flex items-center">
                    <div class="w-6 h-6 bg-secondary rounded-full flex items-center justify-center text-white text-xs mr-3">2</div>
                    数据安全合规
                  </h4>
                  <div class="space-y-2">
                    <p class="text-sm text-gray-700"><strong>提问逻辑：</strong>合规要求→现有措施→潜在风险→解决方案需求</p>
                    <div class="bg-white rounded p-3">
                      <p class="text-sm">"行业对数据存储、员工操作有合规要求吧？现在用什么方式满足这些要求呀？这些方式会不会有遗漏、被监管处罚的风险？"</p>
                    </div>
                  </div>
                </div>

                <!-- 问题链3 -->
                <div class="bg-gray-50 rounded-lg p-4">
                  <h4 class="font-bold text-dark mb-3 flex items-center">
                    <div class="w-6 h-6 bg-success rounded-full flex items-center justify-center text-white text-xs mr-3">3</div>
                    管理效率提升
                  </h4>
                  <div class="space-y-2">
                    <p class="text-sm text-gray-700"><strong>提问逻辑：</strong>现有流程→低效环节→效率损失→提效需求</p>
                    <div class="bg-white rounded p-3">
                      <p class="text-sm">"现在审批、协作流程是线上多还是线下多呀？有没有流程卡壳、信息传递慢，拖慢项目进度的情况？"</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 信息收集维度 -->
        <div class="bg-white rounded-xl shadow-card p-6 mb-8">
          <h3 class="text-xl font-bold text-dark mb-6">信息收集维度（三方面十要素）</h3>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div class="bg-primary/5 rounded-lg p-4">
              <h4 class="font-bold text-primary mb-3">企业侧信息</h4>
              <ul class="space-y-2 text-sm">
                <li class="flex items-start">
                  <i class="fa fa-check-circle text-primary mt-1 mr-2"></i>
                  <span>企业规模（50-500人）</span>
                </li>
                <li class="flex items-start">
                  <i class="fa fa-check-circle text-primary mt-1 mr-2"></i>
                  <span>业务领域（金融/制造等）</span>
                </li>
                <li class="flex items-start">
                  <i class="fa fa-check-circle text-primary mt-1 mr-2"></i>
                  <span>决策角色（IT/管理层）</span>
                </li>
              </ul>
            </div>

            <div class="bg-secondary/5 rounded-lg p-4">
              <h4 class="font-bold text-secondary mb-3">管理侧信息</h4>
              <ul class="space-y-2 text-sm">
                <li class="flex items-start">
                  <i class="fa fa-check-circle text-secondary mt-1 mr-2"></i>
                  <span>管理痛点（安全/效率/协同）</span>
                </li>
                <li class="flex items-start">
                  <i class="fa fa-check-circle text-secondary mt-1 mr-2"></i>
                  <span>性格特点（严谨/务实/创新）</span>
                </li>
                <li class="flex items-start">
                  <i class="fa fa-check-circle text-secondary mt-1 mr-2"></i>
                  <span>管理习惯（制度/系统）</span>
                </li>
              </ul>
            </div>

            <div class="bg-success/5 rounded-lg p-4">
              <h4 class="font-bold text-success mb-3">场景侧信息</h4>
              <ul class="space-y-2 text-sm">
                <li class="flex items-start">
                  <i class="fa fa-check-circle text-success mt-1 mr-2"></i>
                  <span>业务阶段（扩张/维稳）</span>
                </li>
                <li class="flex items-start">
                  <i class="fa fa-check-circle text-success mt-1 mr-2"></i>
                  <span>合规要求（监管/存证）</span>
                </li>
                <li class="flex items-start">
                  <i class="fa fa-check-circle text-success mt-1 mr-2"></i>
                  <span>现有系统（OA/CRM痛点）</span>
                </li>
              </ul>
            </div>
          </div>
        </div>

        <!-- 阶段成功指标 -->
        <div class="bg-success/5 rounded-xl p-6 mb-8">
          <h3 class="text-lg font-bold text-dark mb-4 flex items-center">
            <i class="fa fa-trophy text-success mr-3"></i>
            阶段成功指标
          </h3>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div class="bg-white rounded-lg p-4 text-center">
              <div class="text-2xl font-bold text-primary mb-1">5个</div>
              <div class="text-sm text-gray-600">问题链深度挖掘</div>
            </div>
            <div class="bg-white rounded-lg p-4 text-center">
              <div class="text-2xl font-bold text-secondary mb-1">10项</div>
              <div class="text-sm text-gray-600">信息收集要素</div>
            </div>
            <div class="bg-white rounded-lg p-4 text-center">
              <div class="text-2xl font-bold text-success mb-1">72%</div>
              <div class="text-sm text-gray-600">需求匹配度目标</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 阶段4：邀约演示阶段 -->
      <div class="stage-content hidden" id="stage-4-content">
        <div class="bg-white rounded-xl shadow-card p-6 md:p-8 mb-8">
          <div class="flex flex-col md:flex-row gap-8">
            <div class="md:w-1/3">
              <div class="bg-warning/5 rounded-lg p-6">
                <h3 class="text-xl font-bold text-warning mb-4 flex items-center">
                  <i class="fa fa-calendar mr-3"></i> 邀约策略
                </h3>
                <div class="bg-warning/5 rounded-lg p-4 mb-4">
                  <p class="text-warning font-bold">从"功能演示"到"场景化解决方案演示"</p>
                </div>

                <h4 class="font-bold text-dark mb-4">FABE法则</h4>
                <div class="space-y-3">
                  <div class="bg-white rounded p-3 border-l-4 border-primary">
                    <h5 class="font-medium text-dark">F - 功能特性</h5>
                    <p class="text-xs text-gray-600">产品具体功能</p>
                  </div>
                  <div class="bg-white rounded p-3 border-l-4 border-secondary">
                    <h5 class="font-medium text-dark">A - 优势对比</h5>
                    <p class="text-xs text-gray-600">与竞品差异</p>
                  </div>
                  <div class="bg-white rounded p-3 border-l-4 border-success">
                    <h5 class="font-medium text-dark">B - 客户利益</h5>
                    <p class="text-xs text-gray-600">带来的好处</p>
                  </div>
                  <div class="bg-white rounded p-3 border-l-4 border-warning">
                    <h5 class="font-medium text-dark">E - 证据支撑</h5>
                    <p class="text-xs text-gray-600">案例和数据</p>
                  </div>
                </div>
              </div>
            </div>

            <div class="md:w-2/3">
              <h3 class="text-xl font-bold text-dark mb-6 flex items-center">
                <i class="fa fa-bullhorn mr-3"></i> 邀约话术
              </h3>

              <div class="bg-gray-50 rounded-lg p-6">
                <div class="space-y-4">
                  <div class="bg-white rounded-lg border p-4">
                    <h4 class="font-bold text-dark mb-3">场景化邀约话术</h4>
                    <div class="bg-primary/5 rounded p-3">
                      <p class="text-sm">"王总，那太好了！针对您刚才说的<strong>'客户流失'和'新人成长慢'这两个核心痛点</strong>，我们抽15分钟，在线上给您做个专属演示。</p>
                      <p class="text-sm mt-2">我不讲其他功能，就给您看我们的'工作手机'是怎么把客户锁在公司，以及'AI话术库'是怎么让一个新人3天上手、快速变成专家的。您看周X下午3点方便吗？"</p>
                    </div>
                  </div>

                  <div class="bg-white rounded-lg border p-4">
                    <h4 class="font-bold text-dark mb-3">价值塑造话术</h4>
                    <div class="bg-secondary/5 rounded p-3">
                      <p class="text-sm">"[客户称呼]，基于我们前期的沟通，我认为翼企办的[针对性功能]可能非常适合解决您提到的[客户痛点]问题。</p>
                      <p class="text-sm mt-2">我想邀请您参加一个30分钟的在线演示，具体展示如何通过我们的系统解决这些问题。演示后，您会对我们如何帮助类似企业[提升销售业绩/降低合规风险/保护客户资源]有更直观的了解。"</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 行业针对性演示重点 -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <div class="bg-white rounded-xl shadow-card p-6">
            <h3 class="font-bold text-dark mb-4 flex items-center">
              <i class="fa fa-university text-primary mr-3"></i>
              金融催收/理财保险
            </h3>
            <div class="space-y-3">
              <div class="bg-primary/5 rounded p-3">
                <h5 class="font-medium text-dark">重点展示</h5>
                <p class="text-sm text-gray-600">全流程合规监管与风控功能</p>
              </div>
              <div class="bg-gray-50 rounded p-3">
                <h5 class="font-medium text-dark">案例准备</h5>
                <p class="text-sm text-gray-600">某金融机构降低合规风险的成功案例</p>
              </div>
            </div>
          </div>

          <div class="bg-white rounded-xl shadow-card p-6">
            <h3 class="font-bold text-dark mb-4 flex items-center">
              <i class="fa fa-home text-secondary mr-3"></i>
              地产家装
            </h3>
            <div class="space-y-3">
              <div class="bg-secondary/5 rounded p-3">
                <h5 class="font-medium text-dark">重点展示</h5>
                <p class="text-sm text-gray-600">销售赋能与客户资源保护功能</p>
              </div>
              <div class="bg-gray-50 rounded p-3">
                <h5 class="font-medium text-dark">案例准备</h5>
                <p class="text-sm text-gray-600">某地产公司提升转化率的成功案例</p>
              </div>
            </div>
          </div>

          <div class="bg-white rounded-xl shadow-card p-6">
            <h3 class="font-bold text-dark mb-4 flex items-center">
              <i class="fa fa-graduation-cap text-success mr-3"></i>
              教育培训
            </h3>
            <div class="space-y-3">
              <div class="bg-success/5 rounded p-3">
                <h5 class="font-medium text-dark">重点展示</h5>
                <p class="text-sm text-gray-600">销售流程标准化与效果监控</p>
              </div>
              <div class="bg-gray-50 rounded p-3">
                <h5 class="font-medium text-dark">案例准备</h5>
                <p class="text-sm text-gray-600">某教育机构提升转化率的数据分析</p>
              </div>
            </div>
          </div>
        </div>

        <!-- 阶段成功指标 -->
        <div class="bg-warning/5 rounded-xl p-6 mb-8">
          <h3 class="text-lg font-bold text-dark mb-4 flex items-center">
            <i class="fa fa-trophy text-warning mr-3"></i>
            阶段成功指标
          </h3>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div class="bg-white rounded-lg p-4 text-center">
              <div class="text-2xl font-bold text-primary mb-1">30%</div>
              <div class="text-sm text-gray-600">微信-演示转化率目标</div>
            </div>
            <div class="bg-white rounded-lg p-4 text-center">
              <div class="text-2xl font-bold text-secondary mb-1">30min</div>
              <div class="text-sm text-gray-600">标准演示时长</div>
            </div>
            <div class="bg-white rounded-lg p-4 text-center">
              <div class="text-2xl font-bold text-success mb-1">85%</div>
              <div class="text-sm text-gray-600">演示参与度目标</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 阶段5：方案演示阶段 -->
      <div class="stage-content hidden" id="stage-5-content">
        <div class="bg-white rounded-xl shadow-card p-6 md:p-8 mb-8">
          <div class="flex flex-col md:flex-row gap-8">
            <div class="md:w-1/3">
              <div class="bg-danger/5 rounded-lg p-6">
                <h3 class="text-xl font-bold text-danger mb-4 flex items-center">
                  <i class="fa fa-desktop mr-3"></i> 演示目标
                </h3>
                <div class="bg-danger/5 rounded-lg p-4 mb-4">
                  <p class="text-danger font-bold">证明"我们是最优解"</p>
                </div>

                <h4 class="font-bold text-dark mb-4">方案呈现五步法</h4>
                <div class="space-y-3">
                  <div class="bg-white rounded p-3 border-l-4 border-primary">
                    <h5 class="font-medium text-dark">1. 问题确认</h5>
                    <p class="text-xs text-gray-600">3分钟</p>
                  </div>
                  <div class="bg-white rounded p-3 border-l-4 border-secondary">
                    <h5 class="font-medium text-dark">2. 影响放大</h5>
                    <p class="text-xs text-gray-600">2分钟</p>
                  </div>
                  <div class="bg-white rounded p-3 border-l-4 border-success">
                    <h5 class="font-medium text-dark">3. 解决方案呈现</h5>
                    <p class="text-xs text-gray-600">10分钟</p>
                  </div>
                  <div class="bg-white rounded p-3 border-l-4 border-warning">
                    <h5 class="font-medium text-dark">4. 价值量化</h5>
                    <p class="text-xs text-gray-600">5分钟</p>
                  </div>
                  <div class="bg-white rounded p-3 border-l-4 border-danger">
                    <h5 class="font-medium text-dark">5. 证明与互动</h5>
                    <p class="text-xs text-gray-600">10分钟</p>
                  </div>
                </div>
              </div>
            </div>

            <div class="md:w-2/3">
              <h3 class="text-xl font-bold text-dark mb-6 flex items-center">
                <i class="fa fa-bar-chart mr-3"></i> 核心价值点
              </h3>

              <div class="space-y-4">
                <div class="bg-gray-50 rounded-lg p-4">
                  <h4 class="font-bold text-dark mb-3 flex items-center">
                    <div class="w-6 h-6 bg-primary rounded-full flex items-center justify-center text-white text-xs mr-3">1</div>
                    全流程合规监管与风控
                  </h4>
                  <div class="bg-white rounded p-3">
                    <p class="text-sm">这个功能可以帮助您实时监控所有销售沟通渠道，自动识别违规话术，避免合规风险。根据我们的数据，使用此功能的客户平均减少了78%的投诉率，避免了潜在的罚款风险。</p>
                  </div>
                </div>

                <div class="bg-gray-50 rounded-lg p-4">
                  <h4 class="font-bold text-dark mb-3 flex items-center">
                    <div class="w-6 h-6 bg-secondary rounded-full flex items-center justify-center text-white text-xs mr-3">2</div>
                    销售赋能与客户资源保护
                  </h4>
                  <div class="bg-white rounded p-3">
                    <p class="text-sm">通过AI辅助销售分析，系统能自动识别客户意向并推荐最佳话术，我们的客户平均提升销售转化率达到2倍。同时，所有客户资源都存储在云端，员工离职也不会带走客户资源，彻底解决"飞单"问题。</p>
                  </div>
                </div>

                <div class="bg-gray-50 rounded-lg p-4">
                  <h4 class="font-bold text-dark mb-3 flex items-center">
                    <div class="w-6 h-6 bg-success rounded-full flex items-center justify-center text-white text-xs mr-3">3</div>
                    全业务数据可视化与智能决策
                  </h4>
                  <div class="bg-white rounded p-3">
                    <p class="text-sm">这个智能数据大屏整合了您所有业务系统的数据，让管理层可以实时掌握业绩进度、资源效能等关键指标。AI分析还能自动挖掘高价值线索，提升跟进效率，平均为企业节省30%的管理成本。</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- ROI计算展示 -->
        <div class="bg-white rounded-xl shadow-card p-6 mb-8">
          <h3 class="text-xl font-bold text-dark mb-6 flex items-center">
            <i class="fa fa-calculator mr-3"></i> ROI计算展示模板
          </h3>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div class="bg-primary/5 rounded-lg p-4">
              <h4 class="font-bold text-primary mb-3">投入</h4>
              <p class="text-sm text-gray-600">XX台账号×单价=总投入XX元/年</p>
            </div>

            <div class="bg-secondary/5 rounded-lg p-4">
              <h4 class="font-bold text-secondary mb-3">回报</h4>
              <ul class="text-sm text-gray-600 space-y-1">
                <li>• 提升销售转化率：预计提升XX%，增加收入XX元</li>
                <li>• 减少客户流失：预计减少XX%，挽回价值XX元</li>
                <li>• 降低管理成本：预计节省XX人力，价值XX元</li>
              </ul>
            </div>

            <div class="bg-success/5 rounded-lg p-4">
              <h4 class="font-bold text-success mb-3">投资回报</h4>
              <div class="space-y-2">
                <p class="text-sm text-gray-600">投资回报比：XX:1</p>
                <p class="text-sm text-gray-600">投资回收期：XX个月</p>
                <p class="text-sm text-gray-600 font-medium">这还不包括避免合规风险带来的无形价值</p>
              </div>
            </div>
          </div>
        </div>

        <!-- 阶段成功指标 -->
        <div class="bg-danger/5 rounded-xl p-6 mb-8">
          <h3 class="text-lg font-bold text-dark mb-4 flex items-center">
            <i class="fa fa-trophy text-danger mr-3"></i>
            阶段成功指标
          </h3>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div class="bg-white rounded-lg p-4 text-center">
              <div class="text-2xl font-bold text-primary mb-1">92%</div>
              <div class="text-sm text-gray-600">方案匹配度目标</div>
            </div>
            <div class="bg-white rounded-lg p-4 text-center">
              <div class="text-2xl font-bold text-secondary mb-1">5步法</div>
              <div class="text-sm text-gray-600">标准演示流程</div>
            </div>
            <div class="bg-white rounded-lg p-4 text-center">
              <div class="text-2xl font-bold text-success mb-1">79%</div>
              <div class="text-sm text-gray-600">客户满意度目标</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 阶段6：异议处理阶段 -->
      <div class="stage-content hidden" id="stage-6-content">
        <div class="bg-white rounded-xl shadow-card p-6 md:p-8 mb-8">
          <div class="flex flex-col md:flex-row gap-8">
            <div class="md:w-1/3">
              <div class="bg-info/5 rounded-lg p-6">
                <h3 class="text-xl font-bold text-info mb-4 flex items-center">
                  <i class="fa fa-shield mr-3"></i> LAER框架
                </h3>
                <div class="bg-info/5 rounded-lg p-4 mb-4">
                  <p class="text-info font-bold">系统化异议处理方法</p>
                </div>

                <h4 class="font-bold text-dark mb-4">LAER四步法</h4>
                <div class="space-y-3">
                  <div class="bg-white rounded p-3 border-l-4 border-primary">
                    <h5 class="font-medium text-dark">L - 倾听</h5>
                    <p class="text-xs text-gray-600">完整听取客户顾虑</p>
                  </div>
                  <div class="bg-white rounded p-3 border-l-4 border-secondary">
                    <h5 class="font-medium text-dark">A - 确认</h5>
                    <p class="text-xs text-gray-600">确认理解客户问题</p>
                  </div>
                  <div class="bg-white rounded p-3 border-l-4 border-success">
                    <h5 class="font-medium text-dark">E - 探索</h5>
                    <p class="text-xs text-gray-600">深入了解背后原因</p>
                  </div>
                  <div class="bg-white rounded p-3 border-l-4 border-warning">
                    <h5 class="font-medium text-dark">R - 回应</h5>
                    <p class="text-xs text-gray-600">提供针对性解决方案</p>
                  </div>
                </div>
              </div>
            </div>

            <div class="md:w-2/3">
              <h3 class="text-xl font-bold text-dark mb-6 flex items-center">
                <i class="fa fa-exclamation-triangle mr-3"></i> 5大类常见异议
              </h3>

              <div class="space-y-4">
                <div class="bg-gray-50 rounded-lg p-4">
                  <h4 class="font-bold text-dark mb-3 flex items-center">
                    <div class="w-6 h-6 bg-danger rounded-full flex items-center justify-center text-white text-xs mr-3">1</div>
                    员工抵触监控
                  </h4>
                  <div class="bg-white rounded p-3">
                    <p class="text-sm"><strong>异议：</strong>"员工会觉得被监控，抵触情绪大"</p>
                    <p class="text-sm mt-2"><strong>应对：</strong>这不是监控，是合规保护。就像开车系安全带，不是限制自由，是保障安全。我们系统只监管工作相关行为，私人内容不碰，还能帮员工规范话术，提升业绩。</p>
                  </div>
                </div>

                <div class="bg-gray-50 rounded-lg p-4">
                  <h4 class="font-bold text-dark mb-3 flex items-center">
                    <div class="w-6 h-6 bg-warning rounded-full flex items-center justify-center text-white text-xs mr-3">2</div>
                    产品价格太高
                  </h4>
                  <div class="bg-white rounded p-3">
                    <p class="text-sm"><strong>异议：</strong>"你们价格比别家贵不少"</p>
                    <p class="text-sm mt-2"><strong>应对：</strong>您看的是表面价格，我们是'系统+硬件+服务'一体化方案。办公手机的硬件管控功能，别家纯软件做不到；而且每年免费升级，长期来看，帮您省的隐性成本是价格的3倍。</p>
                  </div>
                </div>

                <div class="bg-gray-50 rounded-lg p-4">
                  <h4 class="font-bold text-dark mb-3 flex items-center">
                    <div class="w-6 h-6 bg-primary rounded-full flex items-center justify-center text-white text-xs mr-3">3</div>
                    担心数据隐私
                  </h4>
                  <div class="bg-white rounded p-3">
                    <p class="text-sm"><strong>异议：</strong>"把数据放你们系统，不安全"</p>
                    <p class="text-sm mt-2"><strong>应对：</strong>我们通过ISO27001数据安全认证，银行级加密存储，数据只在您企业私有云流转，我们系统管理员都无权查看。这是我们的安全认证证书。</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 新异议应对思路 -->
        <div class="bg-white rounded-xl shadow-card p-6 mb-8">
          <h3 class="text-xl font-bold text-dark mb-6">新异议应对思路</h3>
          <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div class="bg-primary/5 rounded-lg p-4 text-center">
              <div class="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center text-primary text-xl mx-auto mb-3">
                <i class="fa fa-heart"></i>
              </div>
              <h4 class="font-bold text-dark mb-2">步骤1：共情认同</h4>
              <p class="text-sm text-gray-600">"您提的问题特别关键，很多客户刚开始也担心"</p>
            </div>

            <div class="bg-secondary/5 rounded-lg p-4 text-center">
              <div class="w-12 h-12 bg-secondary/10 rounded-full flex items-center justify-center text-secondary text-xl mx-auto mb-3">
                <i class="fa fa-puzzle-piece"></i>
              </div>
              <h4 class="font-bold text-dark mb-2">步骤2：拆解问题</h4>
              <p class="text-sm text-gray-600">"其实可以拆成[问题1+问题2]，我们分别看怎么解决"</p>
            </div>

            <div class="bg-success/5 rounded-lg p-4 text-center">
              <div class="w-12 h-12 bg-success/10 rounded-full flex items-center justify-center text-success text-xl mx-auto mb-3">
                <i class="fa fa-lightbulb-o"></i>
              </div>
              <h4 class="font-bold text-dark mb-2">步骤3：关联案例</h4>
              <p class="text-sm text-gray-600">"XX客户遇到过类似问题，用[功能A+服务B]解决了"</p>
            </div>

            <div class="bg-warning/5 rounded-lg p-4 text-center">
              <div class="w-12 h-12 bg-warning/10 rounded-full flex items-center justify-center text-warning text-xl mx-auto mb-3">
                <i class="fa fa-shield"></i>
              </div>
              <h4 class="font-bold text-dark mb-2">步骤4：风险兜底</h4>
              <p class="text-sm text-gray-600">"实在不放心，我们可以[免费试用/小范围试点]"</p>
            </div>
          </div>
        </div>

        <!-- 阶段成功指标 -->
        <div class="bg-info/5 rounded-xl p-6 mb-8">
          <h3 class="text-lg font-bold text-dark mb-4 flex items-center">
            <i class="fa fa-trophy text-info mr-3"></i>
            阶段成功指标
          </h3>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div class="bg-white rounded-lg p-4 text-center">
              <div class="text-2xl font-bold text-primary mb-1">87%</div>
              <div class="text-sm text-gray-600">异议解决率目标</div>
            </div>
            <div class="bg-white rounded-lg p-4 text-center">
              <div class="text-2xl font-bold text-secondary mb-1">LAER</div>
              <div class="text-sm text-gray-600">标准处理框架</div>
            </div>
            <div class="bg-white rounded-lg p-4 text-center">
              <div class="text-2xl font-bold text-success mb-1">85%</div>
              <div class="text-sm text-gray-600">客户信心指数目标</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 阶段7：促成交易阶段 -->
      <div class="stage-content hidden" id="stage-7-content">
        <div class="bg-white rounded-xl shadow-card p-6 md:p-8 mb-8">
          <div class="flex flex-col md:flex-row gap-8">
            <div class="md:w-1/3">
              <div class="bg-success/5 rounded-lg p-6">
                <h3 class="text-xl font-bold text-success mb-4 flex items-center">
                  <i class="fa fa-handshake-o mr-3"></i> 成交策略
                </h3>
                <div class="bg-success/5 rounded-lg p-4 mb-4">
                  <p class="text-success font-bold">三阶段递进式促成</p>
                </div>

                <h4 class="font-bold text-dark mb-4">成交信号识别</h4>
                <div class="space-y-3">
                  <div class="bg-white rounded p-3 border-l-4 border-success">
                    <h5 class="font-medium text-dark">强烈购买信号</h5>
                    <p class="text-xs text-gray-600">需立即推进成交</p>
                  </div>
                  <div class="bg-white rounded p-3 border-l-4 border-warning">
                    <h5 class="font-medium text-dark">中等购买信号</h5>
                    <p class="text-xs text-gray-600">需积极推进下一步</p>
                  </div>
                  <div class="bg-white rounded p-3 border-l-4 border-info">
                    <h5 class="font-medium text-dark">初步购买信号</h5>
                    <p class="text-xs text-gray-600">需继续培育</p>
                  </div>
                </div>
              </div>
            </div>

            <div class="md:w-2/3">
              <h3 class="text-xl font-bold text-dark mb-6 flex items-center">
                <i class="fa fa-trophy mr-3"></i> 成交推进技巧
              </h3>

              <div class="space-y-4">
                <div class="bg-gray-50 rounded-lg p-4">
                  <h4 class="font-bold text-dark mb-3 flex items-center">
                    <div class="w-6 h-6 bg-primary rounded-full flex items-center justify-center text-white text-xs mr-3">1</div>
                    假设性成交技巧
                  </h4>
                  <div class="bg-white rounded p-3">
                    <p class="text-sm">"如果我们今天达成合作，您希望从哪个模块先开始实施？销售赋能还是合规监管？"</p>
                  </div>
                </div>

                <div class="bg-gray-50 rounded-lg p-4">
                  <h4 class="font-bold text-dark mb-3 flex items-center">
                    <div class="w-6 h-6 bg-secondary rounded-full flex items-center justify-center text-white text-xs mr-3">2</div>
                    总结价值促成交
                  </h4>
                  <div class="bg-white rounded p-3">
                    <p class="text-sm">"基于我们的讨论，翼企办能帮助贵公司解决三个核心问题：1)提高销售转化率；2)保护客户资源；3)降低合规风险。投资回报期预计在X个月内。"</p>
                  </div>
                </div>

                <div class="bg-gray-50 rounded-lg p-4">
                  <h4 class="font-bold text-dark mb-3 flex items-center">
                    <div class="w-6 h-6 bg-success rounded-full flex items-center justify-center text-white text-xs mr-3">3</div>
                    紧迫感创造
                  </h4>
                  <div class="bg-white rounded p-3">
                    <p class="text-sm">"目前我们有一个本季度最后的促销活动，可以额外赠送X个月使用期，但需要在本周五前完成签约。"</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 让步层级 -->
        <div class="bg-white rounded-xl shadow-card p-6 mb-8">
          <h3 class="text-xl font-bold text-dark mb-6">谈判策略与让步原则</h3>
          <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
            <div class="bg-primary/5 rounded-lg p-4 text-center">
              <h4 class="font-bold text-primary mb-2">让步1</h4>
              <p class="text-sm text-gray-600">增加免费培训次数</p>
            </div>
            <div class="bg-secondary/5 rounded-lg p-4 text-center">
              <h4 class="font-bold text-secondary mb-2">让步2</h4>
              <p class="text-sm text-gray-600">延长技术支持时间</p>
            </div>
            <div class="bg-success/5 rounded-lg p-4 text-center">
              <h4 class="font-bold text-success mb-2">让步3</h4>
              <p class="text-sm text-gray-600">提供额外模块试用</p>
            </div>
            <div class="bg-warning/5 rounded-lg p-4 text-center">
              <h4 class="font-bold text-warning mb-2">让步4</h4>
              <p class="text-sm text-gray-600">灵活付款方式</p>
            </div>
            <div class="bg-danger/5 rounded-lg p-4 text-center">
              <h4 class="font-bold text-danger mb-2">让步5</h4>
              <p class="text-sm text-gray-600">小幅价格调整(5-10%)</p>
            </div>
          </div>
        </div>

        <!-- 阶段成功指标 -->
        <div class="bg-success/5 rounded-xl p-6 mb-8">
          <h3 class="text-lg font-bold text-dark mb-4 flex items-center">
            <i class="fa fa-trophy text-success mr-3"></i>
            阶段成功指标
          </h3>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div class="bg-white rounded-lg p-4 text-center">
              <div class="text-2xl font-bold text-primary mb-1">65%</div>
              <div class="text-sm text-gray-600">成交转化率目标</div>
            </div>
            <div class="bg-white rounded-lg p-4 text-center">
              <div class="text-2xl font-bold text-secondary mb-1">3种</div>
              <div class="text-sm text-gray-600">成交技巧方法</div>
            </div>
            <div class="bg-white rounded-lg p-4 text-center">
              <div class="text-2xl font-bold text-success mb-1">91%</div>
              <div class="text-sm text-gray-600">合同满意度目标</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 阶段8：客户成功阶段 -->
      <div class="stage-content hidden" id="stage-8-content">
        <div class="bg-white rounded-xl shadow-card p-6 md:p-8 mb-8">
          <div class="flex flex-col md:flex-row gap-8">
            <div class="md:w-1/3">
              <div class="bg-primary/5 rounded-lg p-6">
                <h3 class="text-xl font-bold text-primary mb-4 flex items-center">
                  <i class="fa fa-users mr-3"></i> 客户成功
                </h3>
                <div class="bg-primary/5 rounded-lg p-4 mb-4">
                  <p class="text-primary font-bold">确保服务口碑，创造续费和转介绍</p>
                </div>

                <h4 class="font-bold text-dark mb-4">关键检查点</h4>
                <div class="space-y-3">
                  <div class="bg-white rounded p-3 border-l-4 border-success">
                    <h5 class="font-medium text-dark">7天检查点</h5>
                    <p class="text-xs text-gray-600">基础功能使用情况</p>
                  </div>
                  <div class="bg-white rounded p-3 border-l-4 border-warning">
                    <h5 class="font-medium text-dark">30天检查点</h5>
                    <p class="text-xs text-gray-600">核心功能应用深度</p>
                  </div>
                  <div class="bg-white rounded p-3 border-l-4 border-primary">
                    <h5 class="font-medium text-dark">90天检查点</h5>
                    <p class="text-xs text-gray-600">全面ROI评估</p>
                  </div>
                </div>
              </div>
            </div>

            <div class="md:w-2/3">
              <h3 class="text-xl font-bold text-dark mb-6 flex items-center">
                <i class="fa fa-cogs mr-3"></i> 交付流程管理
              </h3>

              <div class="space-y-4">
                <div class="bg-gray-50 rounded-lg p-4">
                  <h4 class="font-bold text-dark mb-3">交付启动会议议程</h4>
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <ul class="space-y-2 text-sm">
                      <li class="flex items-start">
                        <i class="fa fa-check-circle text-success mt-1 mr-2"></i>
                        <span>项目目标与成功标准确认</span>
                      </li>
                      <li class="flex items-start">
                        <i class="fa fa-check-circle text-success mt-1 mr-2"></i>
                        <span>实施团队与客户团队介绍</span>
                      </li>
                      <li class="flex items-start">
                        <i class="fa fa-check-circle text-success mt-1 mr-2"></i>
                        <span>实施计划与时间表确认</span>
                      </li>
                    </ul>
                    <ul class="space-y-2 text-sm">
                      <li class="flex items-start">
                        <i class="fa fa-check-circle text-success mt-1 mr-2"></i>
                        <span>数据准备与系统对接要求</span>
                      </li>
                      <li class="flex items-start">
                        <i class="fa fa-check-circle text-success mt-1 mr-2"></i>
                        <span>培训计划确认</span>
                      </li>
                      <li class="flex items-start">
                        <i class="fa fa-check-circle text-success mt-1 mr-2"></i>
                        <span>风险预估与应对方案</span>
                      </li>
                    </ul>
                  </div>
                </div>

                <div class="bg-gray-50 rounded-lg p-4">
                  <h4 class="font-bold text-dark mb-3">客户培训计划</h4>
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="space-y-2">
                      <div class="bg-primary/5 rounded p-2">
                        <h5 class="font-medium text-dark">管理层培训</h5>
                        <p class="text-xs text-gray-600">系统价值与数据应用</p>
                      </div>
                      <div class="bg-secondary/5 rounded p-2">
                        <h5 class="font-medium text-dark">销售主管培训</h5>
                        <p class="text-xs text-gray-600">团队管理与数据分析</p>
                      </div>
                    </div>
                    <div class="space-y-2">
                      <div class="bg-success/5 rounded p-2">
                        <h5 class="font-medium text-dark">销售人员培训</h5>
                        <p class="text-xs text-gray-600">日常使用与销售赋能</p>
                      </div>
                      <div class="bg-warning/5 rounded p-2">
                        <h5 class="font-medium text-dark">IT人员培训</h5>
                        <p class="text-xs text-gray-600">系统维护与数据安全</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 复购与推荐 -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
          <div class="bg-white rounded-xl shadow-card p-6">
            <h3 class="font-bold text-dark mb-4 flex items-center">
              <i class="fa fa-refresh text-primary mr-3"></i>
              扩单策略
            </h3>
            <ul class="space-y-3 text-sm">
              <li class="flex items-start">
                <i class="fa fa-arrow-right text-primary mt-1 mr-2"></i>
                <span>基于使用数据推荐适合的扩展模块</span>
              </li>
              <li class="flex items-start">
                <i class="fa fa-arrow-right text-primary mt-1 mr-2"></i>
                <span>针对新业务场景提供解决方案</span>
              </li>
              <li class="flex items-start">
                <i class="fa fa-arrow-right text-primary mt-1 mr-2"></i>
                <span>结合客户业务增长提供扩容方案</span>
              </li>
            </ul>
          </div>

          <div class="bg-white rounded-xl shadow-card p-6">
            <h3 class="font-bold text-dark mb-4 flex items-center">
              <i class="fa fa-share-alt text-secondary mr-3"></i>
              客户推荐引导
            </h3>
            <div class="bg-secondary/5 rounded p-3">
              <p class="text-sm">"看到贵公司通过使用我们的系统已经[取得的具体成果]，非常高兴！我们正在收集行业成功案例，您愿意分享使用心得吗？同时，如果您有类似业务挑战的合作伙伴，我们很乐意为您提供推荐奖励。"</p>
            </div>
          </div>
        </div>

        <!-- 阶段成功指标 -->
        <div class="bg-primary/5 rounded-xl p-6 mb-8">
          <h3 class="text-lg font-bold text-dark mb-4 flex items-center">
            <i class="fa fa-trophy text-primary mr-3"></i>
            阶段成功指标
          </h3>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div class="bg-white rounded-lg p-4 text-center">
              <div class="text-2xl font-bold text-primary mb-1">90天</div>
              <div class="text-sm text-gray-600">客户成功周期</div>
            </div>
            <div class="bg-white rounded-lg p-4 text-center">
              <div class="text-2xl font-bold text-secondary mb-1">4层</div>
              <div class="text-sm text-gray-600">分层培训体系</div>
            </div>
            <div class="bg-white rounded-lg p-4 text-center">
              <div class="text-2xl font-bold text-success mb-1">95%</div>
              <div class="text-sm text-gray-600">客户满意度目标</div>
            </div>
          </div>
        </div>

        <!-- 返回概览 -->
        <div class="text-center mt-8">
          <a href="#overview" class="inline-flex items-center text-primary hover:text-primary/80 transition-colors">
            <i class="fa fa-arrow-up mr-2"></i>
            返回流程概览
          </a>
        </div>
      </div>
    </div>
  </section>

  <!-- 销售资源 -->
  <section id="resources" class="py-16 bg-white">
    <div class="container mx-auto px-4">
      <div class="text-center mb-12">
        <h2 class="text-[clamp(1.5rem,3vw,2.5rem)] font-bold text-dark mb-4">销售资源中心</h2>
        <p class="text-gray-600 max-w-2xl mx-auto">为您提供全面的销售工具、模板和培训资源，助力销售团队高效执行流程。</p>
      </div>
      
      <!-- 资源分类 -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
        <!-- 销售旅程图 -->
        <div class="bg-white rounded-xl shadow-card p-6 hover:shadow-card-hover transition-shadow">
          <div class="flex items-center mb-4">
            <div class="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center text-primary text-xl mr-4">
              <i class="fa fa-map-o"></i>
            </div>
            <div>
              <h3 class="font-bold text-dark">🌍 完整销售旅程图</h3>
              <p class="text-xs text-gray-500">销售流程导航</p>
            </div>
          </div>
          <p class="text-gray-600 text-sm mb-4">从线索获取到客户成功的全旅程指导，包含8个关键阶段的详细操作指南。</p>
          <a href="https://d1jpewyavi.feishu.cn/docx/J2bRdEy1goxgaExCSmFc3c5pnXf?openbrd=1&doc_app_id=501&blockId=Xxs3dXe4kocEkrx4JYtc6xJxncf&blockType=whiteboard&blockToken=QJudwAkmxhzMfQbqDo6cOZ11nOf#Xxs3dXe4kocEkrx4JYtc6xJxncf"
             target="_blank" class="text-primary font-medium hover:underline">
            查看详情 <i class="fa fa-external-link ml-1"></i>
          </a>
        </div>

        <!-- 转化手册 -->
        <div class="bg-white rounded-xl shadow-card p-6 hover:shadow-card-hover transition-shadow">
          <div class="flex items-center mb-4">
            <div class="w-12 h-12 bg-secondary/10 rounded-lg flex items-center justify-center text-secondary text-xl mr-4">
              <i class="fa fa-book"></i>
            </div>
            <div>
              <h3 class="font-bold text-dark">💰 1对1转化手册</h3>
              <p class="text-xs text-gray-500">完整销售手册</p>
            </div>
          </div>
          <p class="text-gray-600 text-sm mb-4">系统化的销售方法论，提供从线索到成交的完整转化策略。</p>
          <a href="https://d1jpewyavi.feishu.cn/docx/J2bRdEy1goxgaExCSmFc3c5pnXf"
             target="_blank" class="text-secondary font-medium hover:underline">
            查看详情 <i class="fa fa-external-link ml-1"></i>
          </a>
        </div>

        <!-- 行业解决方案 -->
        <div class="bg-white rounded-xl shadow-card p-6 hover:shadow-card-hover transition-shadow">
          <div class="flex items-center mb-4">
            <div class="w-12 h-12 bg-success/10 rounded-lg flex items-center justify-center text-success text-xl mr-4">
              <i class="fa fa-industry"></i>
            </div>
            <div>
              <h3 class="font-bold text-dark">🚩 专属行业方案</h3>
              <p class="text-xs text-gray-500">3个行业解决方案</p>
            </div>
          </div>
          <p class="text-gray-600 text-sm mb-4">针对金融、地产、教育三大行业的定制化销售策略和解决方案。</p>
          <a href="https://d1jpewyavi.feishu.cn/docx/Z2MSd0zpQo6RwlxniGgcxnzNnlb"
             target="_blank" class="text-success font-medium hover:underline">
            查看详情 <i class="fa fa-external-link ml-1"></i>
          </a>
        </div>

        <!-- SOP手册 -->
        <div class="bg-white rounded-xl shadow-card p-6 hover:shadow-card-hover transition-shadow">
          <div class="flex items-center mb-4">
            <div class="w-12 h-12 bg-warning/10 rounded-lg flex items-center justify-center text-warning text-xl mr-4">
              <i class="fa fa-list-alt"></i>
            </div>
            <div>
              <h3 class="font-bold text-dark">🫧 SOP序列手册</h3>
              <p class="text-xs text-gray-500">5大内容SOP</p>
            </div>
          </div>
          <p class="text-gray-600 text-sm mb-4">标准化的操作流程指导，确保销售过程的一致性和高效性。</p>
          <a href="https://d1jpewyavi.feishu.cn/docx/FNMYdM2ROoWlqvxwqk0cP7srnug"
             target="_blank" class="text-warning font-medium hover:underline">
            查看详情 <i class="fa fa-external-link ml-1"></i>
          </a>
        </div>

        <!-- ICP构建 -->
        <div class="bg-white rounded-xl shadow-card p-6 hover:shadow-card-hover transition-shadow">
          <div class="flex items-center mb-4">
            <div class="w-12 h-12 bg-info/10 rounded-lg flex items-center justify-center text-info text-xl mr-4">
              <i class="fa fa-users"></i>
            </div>
            <div>
              <h3 class="font-bold text-dark">🤵‍♂️ ICP构建指南</h3>
              <p class="text-xs text-gray-500">理想客户画像</p>
            </div>
          </div>
          <p class="text-gray-600 text-sm mb-4">理想客户画像构建指南，帮助精准定位和识别目标客户。</p>
          <a href="https://d1jpewyavi.feishu.cn/docx/JjORd53sqo6wMUxK1GkcXflBn4q"
             target="_blank" class="text-info font-medium hover:underline">
            查看详情 <i class="fa fa-external-link ml-1"></i>
          </a>
        </div>

        <!-- 私域标签体系 -->
        <div class="bg-white rounded-xl shadow-card p-6 hover:shadow-card-hover transition-shadow">
          <div class="flex items-center mb-4">
            <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center text-purple-600 text-xl mr-4">
              <i class="fa fa-tags"></i>
            </div>
            <div>
              <h3 class="font-bold text-dark">🏷 私域标签体系</h3>
              <p class="text-xs text-gray-500">标签操作手册</p>
            </div>
          </div>
          <p class="text-gray-600 text-sm mb-4">私域客户标签体系构建和操作手册，提升客户管理效率。</p>
          <a href="https://d1jpewyavi.feishu.cn/docx/Puyxd3hCNo720qxlbjPcJtuVnph"
             target="_blank" class="text-purple-600 font-medium hover:underline">
            查看详情 <i class="fa fa-external-link ml-1"></i>
          </a>
        </div>

        <!-- 客户案例库 -->
        <div class="bg-white rounded-xl shadow-card p-6 hover:shadow-card-hover transition-shadow">
          <div class="flex items-center mb-4">
            <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center text-green-600 text-xl mr-4">
              <i class="fa fa-star"></i>
            </div>
            <div>
              <h3 class="font-bold text-dark">👍 好评案例库</h3>
              <p class="text-xs text-gray-500">客户成功案例</p>
            </div>
          </div>
          <p class="text-gray-600 text-sm mb-4">客户好评和成功案例库，为销售提供有力的社会证明。</p>
          <a href="https://d1jpewyavi.feishu.cn/docx/Qp8mddFIMoqztSxQDgFc8HntnCf"
             target="_blank" class="text-green-600 font-medium hover:underline">
            查看详情 <i class="fa fa-external-link ml-1"></i>
          </a>
        </div>

        <!-- 异议处理话术库 -->
        <div class="bg-white rounded-xl shadow-card p-6 hover:shadow-card-hover transition-shadow">
          <div class="flex items-center mb-4">
            <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center text-red-600 text-xl mr-4">
              <i class="fa fa-shield"></i>
            </div>
            <div>
              <h3 class="font-bold text-dark">💡 异议处理话术</h3>
              <p class="text-xs text-gray-500">10大异议应对</p>
            </div>
          </div>
          <p class="text-gray-600 text-sm mb-4">10大异议处理话术库，系统化的异议应对策略和标准话术。</p>
          <a href="https://d1jpewyavi.feishu.cn/docx/KWd8dfRBWoDzW3x35s1cn8n4nRf"
             target="_blank" class="text-red-600 font-medium hover:underline">
            查看详情 <i class="fa fa-external-link ml-1"></i>
          </a>
        </div>

        <!-- 销售模板库 -->
        <div class="bg-white rounded-xl shadow-card p-6 hover:shadow-card-hover transition-shadow">
          <div class="flex items-center mb-4">
            <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center text-blue-600 text-xl mr-4">
              <i class="fa fa-file-text-o"></i>
            </div>
            <div>
              <h3 class="font-bold text-dark">📂 销售模板库</h3>
              <p class="text-xs text-gray-500">提效模板应用</p>
            </div>
          </div>
          <p class="text-gray-600 text-sm mb-4">销售提效模板应用库，包含各种实用的销售工具和模板。</p>
          <a href="https://d1jpewyavi.feishu.cn/docx/QV3BdFMHjo2bnsxoQAOcCbZqni0"
             target="_blank" class="text-blue-600 font-medium hover:underline">
            查看详情 <i class="fa fa-external-link ml-1"></i>
          </a>
        </div>
      </div>
      
      <!-- 精选资源 -->
      <h3 class="text-xl font-bold text-dark mb-6">精选资源</h3>
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <div class="bg-white rounded-xl shadow-card overflow-hidden hover:shadow-card-hover transition-shadow">
          <div class="h-40 bg-primary/20 relative">
            <img src="https://picsum.photos/seed/sales1/400/200" alt="BANT评分模板" class="w-full h-full object-cover">
            <div class="absolute top-3 left-3 bg-primary text-white text-xs font-medium px-2 py-1 rounded">
              模板
            </div>
          </div>
          <div class="p-5">
            <h4 class="font-bold text-dark mb-2">BANT线索评分模板</h4>
            <p class="text-gray-600 text-sm mb-4">标准化的线索评分表，包含预算、权限、需求、时间四个维度的详细评估标准。</p>
            <div class="flex items-center justify-between">
              <span class="text-xs text-gray-500">更新于 2025-07-20</span>
              <a href="https://d1jpewyavi.feishu.cn/docx/J2bRdEy1goxgaExCSmFc3c5pnXf" target="_blank" class="text-primary text-sm hover:underline">下载 <i class="fa fa-external-link ml-1"></i></a>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-xl shadow-card overflow-hidden hover:shadow-card-hover transition-shadow">
          <div class="h-40 bg-primary/20 relative">
            <img src="https://picsum.photos/seed/sales2/400/200" alt="SPIN销售话术" class="w-full h-full object-cover">
            <div class="absolute top-3 left-3 bg-secondary text-white text-xs font-medium px-2 py-1 rounded">
              话术
            </div>
          </div>
          <div class="p-5">
            <h4 class="font-bold text-dark mb-2">SPIN销售话术库</h4>
            <p class="text-gray-600 text-sm mb-4">包含情境、问题、影响、需求四类问题的完整话术模板，适用于各行业客户。</p>
            <div class="flex items-center justify-between">
              <span class="text-xs text-gray-500">更新于 2025-07-18</span>
              <a href="https://d1jpewyavi.feishu.cn/docx/KWd8dfRBWoDzW3x35s1cn8n4nRf" target="_blank" class="text-secondary text-sm hover:underline">查看 <i class="fa fa-external-link ml-1"></i></a>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-xl shadow-card overflow-hidden hover:shadow-card-hover transition-shadow">
          <div class="h-40 bg-primary/20 relative">
            <img src="https://picsum.photos/seed/sales3/400/200" alt="LAER异议处理" class="w-full h-full object-cover">
            <div class="absolute top-3 left-3 bg-success text-white text-xs font-medium px-2 py-1 rounded">
              框架
            </div>
          </div>
          <div class="p-5">
            <h4 class="font-bold text-dark mb-2">LAER异议处理框架</h4>
            <p class="text-gray-600 text-sm mb-4">系统化的异议处理方法，包含5大类常见异议的标准应对策略。</p>
            <div class="flex items-center justify-between">
              <span class="text-xs text-gray-500">更新于 2025-07-15</span>
              <a href="https://d1jpewyavi.feishu.cn/docx/KWd8dfRBWoDzW3x35s1cn8n4nRf" target="_blank" class="text-success text-sm hover:underline">学习 <i class="fa fa-external-link ml-1"></i></a>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-xl shadow-card overflow-hidden hover:shadow-card-hover transition-shadow">
          <div class="h-40 bg-primary/20 relative">
            <img src="https://picsum.photos/seed/sales4/400/200" alt="成交信号识别" class="w-full h-full object-cover">
            <div class="absolute top-3 left-3 bg-warning text-white text-xs font-medium px-2 py-1 rounded">
              工具
            </div>
          </div>
          <div class="p-5">
            <h4 class="font-bold text-dark mb-2">成交信号识别清单</h4>
            <p class="text-gray-600 text-sm mb-4">详细的购买信号识别指南，帮助销售准确判断客户购买意向强度。</p>
            <div class="flex items-center justify-between">
              <span class="text-xs text-gray-500">更新于 2025-07-12</span>
              <a href="https://d1jpewyavi.feishu.cn/docx/J2bRdEy1goxgaExCSmFc3c5pnXf" target="_blank" class="text-warning text-sm hover:underline">下载 <i class="fa fa-external-link ml-1"></i></a>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-xl shadow-card overflow-hidden hover:shadow-card-hover transition-shadow">
          <div class="h-40 bg-primary/20 relative">
            <img src="https://picsum.photos/seed/sales5/400/200" alt="ROI计算器" class="w-full h-full object-cover">
            <div class="absolute top-3 left-3 bg-danger text-white text-xs font-medium px-2 py-1 rounded">
              计算器
            </div>
          </div>
          <div class="p-5">
            <h4 class="font-bold text-dark mb-2">ROI价值计算器</h4>
            <p class="text-gray-600 text-sm mb-4">自动化的投资回报率计算工具，帮助客户量化方案价值。</p>
            <div class="flex items-center justify-between">
              <span class="text-xs text-gray-500">更新于 2025-07-08</span>
              <button onclick="document.getElementById('kpi-calculator').click()" class="text-danger text-sm hover:underline">使用 <i class="fa fa-calculator ml-1"></i></button>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-xl shadow-card overflow-hidden hover:shadow-card-hover transition-shadow">
          <div class="h-40 bg-primary/20 relative">
            <img src="https://picsum.photos/seed/sales6/400/200" alt="客户成功路径" class="w-full h-full object-cover">
            <div class="absolute top-3 left-3 bg-info text-white text-xs font-medium px-2 py-1 rounded">
              流程
            </div>
          </div>
          <div class="p-5">
            <h4 class="font-bold text-dark mb-2">客户成功管理流程</h4>
            <p class="text-gray-600 text-sm mb-4">从签约到续费的完整客户成功管理流程，包含关键检查点和行动指南。</p>
            <div class="flex items-center justify-between">
              <span class="text-xs text-gray-500">更新于 2025-07-05</span>
              <a href="https://d1jpewyavi.feishu.cn/docx/J2bRdEy1goxgaExCSmFc3c5pnXf" target="_blank" class="text-info text-sm hover:underline">查看 <i class="fa fa-external-link ml-1"></i></a>
            </div>
          </div>
        </div>
      </div>

        <!-- 实战案例库 -->
        <div class="mt-16">
          <h3 class="text-xl font-bold text-dark mb-6 flex items-center">
            <i class="fa fa-lightbulb-o text-warning mr-3"></i>
            实战案例库
          </h3>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
            <div class="bg-gradient-to-r from-primary/5 to-secondary/5 rounded-xl p-6">
              <h4 class="font-bold text-dark mb-4">成功案例分析</h4>
              <div class="space-y-4">
                <div class="bg-white rounded-lg p-4 border-l-4 border-primary">
                  <h5 class="font-medium text-dark mb-2">金融行业：某保险公司销售转化率提升案例</h5>
                  <p class="text-sm text-gray-600 mb-2">通过实施标准化销售流程，3个月内整体转化率从2.1%提升至4.8%</p>
                  <div class="flex items-center justify-between">
                    <span class="text-xs bg-primary/10 text-primary px-2 py-1 rounded">转化率提升128%</span>
                    <a href="https://d1jpewyavi.feishu.cn/docx/Qp8mddFIMoqztSxQDgFc8HntnCf" target="_blank" class="text-primary text-sm hover:underline">查看详情 <i class="fa fa-external-link ml-1"></i></a>
                  </div>
                </div>

                <div class="bg-white rounded-lg p-4 border-l-4 border-secondary">
                  <h5 class="font-medium text-dark mb-2">地产行业：某房地产公司客户资源保护案例</h5>
                  <p class="text-sm text-gray-600 mb-2">通过工作手机管控，有效防止飞单，年度挽回客户价值超过500万</p>
                  <div class="flex items-center justify-between">
                    <span class="text-xs bg-secondary/10 text-secondary px-2 py-1 rounded">飞单率降低85%</span>
                    <a href="https://d1jpewyavi.feishu.cn/docx/Qp8mddFIMoqztSxQDgFc8HntnCf" target="_blank" class="text-secondary text-sm hover:underline">查看详情 <i class="fa fa-external-link ml-1"></i></a>
                  </div>
                </div>

                <div class="bg-white rounded-lg p-4 border-l-4 border-success">
                  <h5 class="font-medium text-dark mb-2">教育行业：某培训机构销售效率优化案例</h5>
                  <p class="text-sm text-gray-600 mb-2">通过AI话术推荐和数据分析，销售人均产出提升65%</p>
                  <div class="flex items-center justify-between">
                    <span class="text-xs bg-success/10 text-success px-2 py-1 rounded">人均产出+65%</span>
                    <a href="https://d1jpewyavi.feishu.cn/docx/Qp8mddFIMoqztSxQDgFc8HntnCf" target="_blank" class="text-success text-sm hover:underline">查看详情 <i class="fa fa-external-link ml-1"></i></a>
                  </div>
                </div>
              </div>
            </div>

            <div class="bg-gradient-to-r from-warning/5 to-danger/5 rounded-xl p-6">
              <h4 class="font-bold text-dark mb-4">失败案例反思</h4>
              <div class="space-y-4">
                <div class="bg-white rounded-lg p-4 border-l-4 border-warning">
                  <h5 class="font-medium text-dark mb-2">案例：忽视需求挖掘导致的演示失败</h5>
                  <p class="text-sm text-gray-600 mb-2">销售急于展示产品功能，未充分了解客户痛点，演示效果不佳</p>
                  <div class="flex items-center justify-between">
                    <span class="text-xs bg-warning/10 text-warning px-2 py-1 rounded">教训总结</span>
                    <a href="https://d1jpewyavi.feishu.cn/docx/KWd8dfRBWoDzW3x35s1cn8n4nRf" target="_blank" class="text-warning text-sm hover:underline">学习要点 <i class="fa fa-external-link ml-1"></i></a>
                  </div>
                </div>

                <div class="bg-white rounded-lg p-4 border-l-4 border-danger">
                  <h5 class="font-medium text-dark mb-2">案例：异议处理不当导致的客户流失</h5>
                  <p class="text-sm text-gray-600 mb-2">面对价格异议时过于强硬，未能有效化解客户顾虑</p>
                  <div class="flex items-center justify-between">
                    <span class="text-xs bg-danger/10 text-danger px-2 py-1 rounded">避免误区</span>
                    <a href="https://d1jpewyavi.feishu.cn/docx/KWd8dfRBWoDzW3x35s1cn8n4nRf" target="_blank" class="text-danger text-sm hover:underline">学习要点 <i class="fa fa-external-link ml-1"></i></a>
                  </div>
                </div>

                <div class="bg-white rounded-lg p-4 border-l-4 border-info">
                  <h5 class="font-medium text-dark mb-2">案例：跟进不及时导致的机会丢失</h5>
                  <p class="text-sm text-gray-600 mb-2">A级线索响应延迟，被竞争对手抢先成交</p>
                  <div class="flex items-center justify-between">
                    <span class="text-xs bg-info/10 text-info px-2 py-1 rounded">时机把握</span>
                    <a href="https://d1jpewyavi.feishu.cn/docx/J2bRdEy1goxgaExCSmFc3c5pnXf" target="_blank" class="text-info text-sm hover:underline">学习要点 <i class="fa fa-external-link ml-1"></i></a>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 行业解决方案 -->
        <div class="mt-16">
          <h3 class="text-xl font-bold text-dark mb-6 flex items-center">
            <i class="fa fa-industry text-primary mr-3"></i>
            行业解决方案
          </h3>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <div class="bg-white rounded-xl shadow-card p-6 hover:shadow-card-hover transition-shadow">
              <div class="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center text-primary text-xl mb-4">
                <i class="fa fa-university"></i>
              </div>
              <h4 class="font-bold text-dark mb-3">金融保险</h4>
              <p class="text-gray-600 text-sm mb-4">针对金融催收、理财保险行业的专业销售解决方案，重点关注合规监管和风险控制。</p>
              <ul class="text-sm text-gray-600 space-y-1 mb-4">
                <li>• 全流程合规话术库</li>
                <li>• 风险评估工具</li>
                <li>• 监管要求检查清单</li>
              </ul>
              <a href="https://d1jpewyavi.feishu.cn/docx/Z2MSd0zpQo6RwlxniGgcxnzNnlb" target="_blank" class="text-primary font-medium hover:underline">查看方案详情 → <i class="fa fa-external-link ml-1"></i></a>
            </div>

            <div class="bg-white rounded-xl shadow-card p-6 hover:shadow-card-hover transition-shadow">
              <div class="w-12 h-12 bg-secondary/10 rounded-lg flex items-center justify-center text-secondary text-xl mb-4">
                <i class="fa fa-home"></i>
              </div>
              <h4 class="font-bold text-dark mb-3">地产家装</h4>
              <p class="text-gray-600 text-sm mb-4">专为地产家装行业设计的销售管理方案，重点解决客户资源保护和转化率提升。</p>
              <ul class="text-sm text-gray-600 space-y-1 mb-4">
                <li>• 客户资源防飞单系统</li>
                <li>• 装修进度跟踪工具</li>
                <li>• 客户满意度管理</li>
              </ul>
              <a href="https://d1jpewyavi.feishu.cn/docx/Z2MSd0zpQo6RwlxniGgcxnzNnlb" target="_blank" class="text-secondary font-medium hover:underline">查看方案详情 → <i class="fa fa-external-link ml-1"></i></a>
            </div>

            <div class="bg-white rounded-xl shadow-card p-6 hover:shadow-card-hover transition-shadow">
              <div class="w-12 h-12 bg-success/10 rounded-lg flex items-center justify-center text-success text-xl mb-4">
                <i class="fa fa-graduation-cap"></i>
              </div>
              <h4 class="font-bold text-dark mb-3">教育培训</h4>
              <p class="text-gray-600 text-sm mb-4">教育培训机构专用销售系统，专注学员转化和续费管理，提升教育服务质量。</p>
              <ul class="text-sm text-gray-600 space-y-1 mb-4">
                <li>• 学员生命周期管理</li>
                <li>• 课程推荐引擎</li>
                <li>• 续费预警系统</li>
              </ul>
              <a href="https://d1jpewyavi.feishu.cn/docx/Z2MSd0zpQo6RwlxniGgcxnzNnlb" target="_blank" class="text-success font-medium hover:underline">查看方案详情 → <i class="fa fa-external-link ml-1"></i></a>
            </div>
          </div>
        </div>

        <!-- 销售工具集成 -->
        <div class="mt-16">
          <h3 class="text-xl font-bold text-dark mb-6 flex items-center">
            <i class="fa fa-cogs text-secondary mr-3"></i>
            销售工具集成
          </h3>
          <div class="bg-gray-50 rounded-xl p-8">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div class="bg-white rounded-lg p-4 text-center shadow-sm hover:shadow-md transition-shadow">
                <div class="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center text-primary text-2xl mx-auto mb-3">
                  <i class="fa fa-wechat"></i>
                </div>
                <h5 class="font-medium text-dark mb-2">微信集成</h5>
                <p class="text-xs text-gray-600">企业微信、个人微信管理工具</p>
              </div>

              <div class="bg-white rounded-lg p-4 text-center shadow-sm hover:shadow-md transition-shadow">
                <div class="w-16 h-16 bg-secondary/10 rounded-full flex items-center justify-center text-secondary text-2xl mx-auto mb-3">
                  <i class="fa fa-phone"></i>
                </div>
                <h5 class="font-medium text-dark mb-2">电话系统</h5>
                <p class="text-xs text-gray-600">智能外呼、通话录音分析</p>
              </div>

              <div class="bg-white rounded-lg p-4 text-center shadow-sm hover:shadow-md transition-shadow">
                <div class="w-16 h-16 bg-success/10 rounded-full flex items-center justify-center text-success text-2xl mx-auto mb-3">
                  <i class="fa fa-envelope"></i>
                </div>
                <h5 class="font-medium text-dark mb-2">邮件营销</h5>
                <p class="text-xs text-gray-600">自动化邮件序列、效果追踪</p>
              </div>

              <div class="bg-white rounded-lg p-4 text-center shadow-sm hover:shadow-md transition-shadow">
                <div class="w-16 h-16 bg-warning/10 rounded-full flex items-center justify-center text-warning text-2xl mx-auto mb-3">
                  <i class="fa fa-database"></i>
                </div>
                <h5 class="font-medium text-dark mb-2">CRM系统</h5>
                <p class="text-xs text-gray-600">客户关系管理、数据同步</p>
              </div>
            </div>

            <div class="mt-8 text-center">
              <h4 class="font-bold text-dark mb-4">一站式销售管理平台</h4>
              <p class="text-gray-600 mb-6">整合多种销售工具，打造高效的销售管理生态系统</p>
              <div class="flex flex-wrap justify-center gap-4">
                <button class="bg-primary text-white px-6 py-2 rounded-lg hover:bg-primary/90 transition-colors">
                  申请试用 <i class="fa fa-arrow-right ml-2"></i>
                </button>
                <button class="border border-primary text-primary px-6 py-2 rounded-lg hover:bg-primary/5 transition-colors">
                  查看演示 <i class="fa fa-play ml-2"></i>
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- 销售知识库 -->
        <div class="mt-16">
          <h3 class="text-xl font-bold text-dark mb-6 flex items-center">
            <i class="fa fa-book text-success mr-3"></i>
            销售知识库
          </h3>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
            <!-- 常见问题 -->
            <div class="bg-white rounded-xl shadow-card p-6">
              <h4 class="font-bold text-dark mb-4 flex items-center">
                <i class="fa fa-question-circle text-primary mr-2"></i>
                常见问题解答
              </h4>
              <div class="space-y-4">
                <div class="border-b border-gray-100 pb-4">
                  <button class="faq-question w-full text-left font-medium text-dark hover:text-primary transition-colors flex items-center justify-between" data-target="faq-1">
                    如何提高线索转化率？
                    <i class="fa fa-chevron-down text-sm"></i>
                  </button>
                  <div id="faq-1" class="faq-answer hidden mt-3 text-sm text-gray-600">
                    <p>提高线索转化率的关键在于：1) 精准的BANT评分，确保跟进高质量线索；2) 快速响应，A级线索24小时内联系；3) 个性化沟通，根据客户特点调整话术；4) 持续跟进，建立信任关系。</p>
                  </div>
                </div>

                <div class="border-b border-gray-100 pb-4">
                  <button class="faq-question w-full text-left font-medium text-dark hover:text-primary transition-colors flex items-center justify-between" data-target="faq-2">
                    客户说"考虑一下"怎么办？
                    <i class="fa fa-chevron-down text-sm"></i>
                  </button>
                  <div id="faq-2" class="faq-answer hidden mt-3 text-sm text-gray-600">
                    <p>这是典型的信任异议。应对策略：1) 倾听并确认客户的顾虑；2) 探索具体的考虑因素；3) 提供相关案例和证明材料；4) 设定明确的跟进时间点；5) 提供试用或保证等降低风险的方案。</p>
                  </div>
                </div>

                <div class="border-b border-gray-100 pb-4">
                  <button class="faq-question w-full text-left font-medium text-dark hover:text-primary transition-colors flex items-center justify-between" data-target="faq-3">
                    如何判断客户的购买信号？
                    <i class="fa fa-chevron-down text-sm"></i>
                  </button>
                  <div id="faq-3" class="faq-answer hidden mt-3 text-sm text-gray-600">
                    <p>购买信号分为三级：强烈信号（询问价格、付款方式、实施时间）；中等信号（要求演示、索要资料、介绍团队）；初步信号（表示兴趣、提出问题、参与讨论）。识别信号后要及时跟进促成。</p>
                  </div>
                </div>

                <div class="border-b border-gray-100 pb-4">
                  <button class="faq-question w-full text-left font-medium text-dark hover:text-primary transition-colors flex items-center justify-between" data-target="faq-4">
                    演示效果不好怎么改进？
                    <i class="fa fa-chevron-down text-sm"></i>
                  </button>
                  <div id="faq-4" class="faq-answer hidden mt-3 text-sm text-gray-600">
                    <p>改进演示效果的方法：1) 演示前充分了解客户需求；2) 设计场景化的演示内容；3) 使用FABE法则突出价值；4) 增加互动环节，让客户参与；5) 准备充分的案例和数据支撑；6) 演示后及时收集反馈。</p>
                  </div>
                </div>

                <div>
                  <button class="faq-question w-full text-left font-medium text-dark hover:text-primary transition-colors flex items-center justify-between" data-target="faq-5">
                    如何建立长期客户关系？
                    <i class="fa fa-chevron-down text-sm"></i>
                  </button>
                  <div id="faq-5" class="faq-answer hidden mt-3 text-sm text-gray-600">
                    <p>建立长期关系的要点：1) 确保客户成功实施并获得价值；2) 定期回访和关怀；3) 提供持续的培训和支持；4) 主动分享行业洞察和最佳实践；5) 及时响应客户需求；6) 挖掘扩单和推荐机会。</p>
                  </div>
                </div>
              </div>
            </div>

            <!-- 销售技巧速查 -->
            <div class="bg-white rounded-xl shadow-card p-6">
              <h4 class="font-bold text-dark mb-4 flex items-center">
                <i class="fa fa-flash text-warning mr-2"></i>
                销售技巧速查
              </h4>
              <div class="space-y-4">
                <div class="bg-primary/5 rounded-lg p-4">
                  <h5 class="font-medium text-primary mb-2">SPIN提问技巧</h5>
                  <ul class="text-sm text-gray-600 space-y-1">
                    <li><strong>S-情境:</strong> "目前贵公司是如何管理客户的？"</li>
                    <li><strong>P-问题:</strong> "这种方式有什么不便之处吗？"</li>
                    <li><strong>I-影响:</strong> "这个问题对业务有什么影响？"</li>
                    <li><strong>N-需求:</strong> "如果能解决这个问题，对您有什么帮助？"</li>
                  </ul>
                </div>

                <div class="bg-secondary/5 rounded-lg p-4">
                  <h5 class="font-medium text-secondary mb-2">LAER异议处理</h5>
                  <ul class="text-sm text-gray-600 space-y-1">
                    <li><strong>L-倾听:</strong> 认真听取客户的异议</li>
                    <li><strong>A-确认:</strong> 确认理解客户的顾虑</li>
                    <li><strong>E-探索:</strong> 深入了解异议背后的原因</li>
                    <li><strong>R-回应:</strong> 针对性地解决客户顾虑</li>
                  </ul>
                </div>

                <div class="bg-success/5 rounded-lg p-4">
                  <h5 class="font-medium text-success mb-2">FABE价值呈现</h5>
                  <ul class="text-sm text-gray-600 space-y-1">
                    <li><strong>F-功能:</strong> 产品具备什么功能</li>
                    <li><strong>A-优势:</strong> 相比竞品的优势</li>
                    <li><strong>B-利益:</strong> 给客户带来的好处</li>
                    <li><strong>E-证据:</strong> 案例和数据证明</li>
                  </ul>
                </div>

                <div class="bg-warning/5 rounded-lg p-4">
                  <h5 class="font-medium text-warning mb-2">成交信号识别</h5>
                  <ul class="text-sm text-gray-600 space-y-1">
                    <li><strong>语言信号:</strong> "价格怎么算？" "什么时候开始？"</li>
                    <li><strong>行为信号:</strong> 仔细阅读合同、询问细节</li>
                    <li><strong>情绪信号:</strong> 表现兴奋、急迫</li>
                    <li><strong>决策信号:</strong> 邀请决策者参与</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 学习资源推荐 -->
        <div class="mt-16">
          <h3 class="text-xl font-bold text-dark mb-6 flex items-center">
            <i class="fa fa-graduation-cap text-info mr-3"></i>
            推荐学习资源
          </h3>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div class="bg-gradient-to-br from-primary/5 to-primary/10 rounded-xl p-6">
              <div class="w-12 h-12 bg-primary/20 rounded-lg flex items-center justify-center text-primary text-xl mb-4">
                <i class="fa fa-video-camera"></i>
              </div>
              <h4 class="font-bold text-dark mb-3">视频教程</h4>
              <ul class="text-sm text-gray-600 space-y-2 mb-4">
                <li>• SPIN销售法实战应用</li>
                <li>• 异议处理经典案例</li>
                <li>• 成交技巧视频合集</li>
                <li>• 客户心理分析课程</li>
              </ul>
              <a href="#" class="text-primary font-medium hover:underline">观看视频 →</a>
            </div>

            <div class="bg-gradient-to-br from-secondary/5 to-secondary/10 rounded-xl p-6">
              <div class="w-12 h-12 bg-secondary/20 rounded-lg flex items-center justify-center text-secondary text-xl mb-4">
                <i class="fa fa-file-pdf-o"></i>
              </div>
              <h4 class="font-bold text-dark mb-3">电子书籍</h4>
              <ul class="text-sm text-gray-600 space-y-2 mb-4">
                <li>• 《销售心理学》</li>
                <li>• 《顾问式销售指南》</li>
                <li>• 《客户关系管理》</li>
                <li>• 《销售数据分析》</li>
              </ul>
              <a href="#" class="text-secondary font-medium hover:underline">下载阅读 →</a>
            </div>

            <div class="bg-gradient-to-br from-success/5 to-success/10 rounded-xl p-6">
              <div class="w-12 h-12 bg-success/20 rounded-lg flex items-center justify-center text-success text-xl mb-4">
                <i class="fa fa-users"></i>
              </div>
              <h4 class="font-bold text-dark mb-3">在线培训</h4>
              <ul class="text-sm text-gray-600 space-y-2 mb-4">
                <li>• 销售技能认证课程</li>
                <li>• 行业专家直播分享</li>
                <li>• 销售团队训练营</li>
                <li>• 一对一销售辅导</li>
              </ul>
              <a href="#" class="text-success font-medium hover:underline">报名参加 →</a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- 联系我们 -->
  <section id="contact" class="py-16 bg-gray-50">
    <div class="container mx-auto px-4">
      <div class="max-w-4xl mx-auto">
        <div class="text-center mb-12">
          <h2 class="text-[clamp(1.5rem,3vw,2.5rem)] font-bold text-dark mb-4">联系我们</h2>
          <p class="text-gray-600 max-w-2xl mx-auto">有任何问题或需要进一步的支持？请随时联系我们的销售支持团队。</p>
        </div>
        
        <div class="bg-white rounded-xl shadow-card p-6 md:p-8">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div>
              <h3 class="font-bold text-dark text-lg mb-4">销售支持团队</h3>
              <p class="text-gray-600 mb-6">我们的专业销售支持团队随时为您提供帮助，解答疑问，提供培训和资源。</p>
              
              <div class="space-y-4">
                <div class="flex items-start">
                  <div class="text-primary text-xl mt-1 mr-4">
                    <i class="fa fa-envelope-o"></i>
                  </div>
                  <div>
                    <h4 class="font-medium text-dark">电子邮件</h4>
                    <p class="text-gray-600"><EMAIL></p>
                  </div>
                </div>
                
                <div class="flex items-start">
                  <div class="text-primary text-xl mt-1 mr-4">
                    <i class="fa fa-phone"></i>
                  </div>
                  <div>
                    <h4 class="font-medium text-dark">电话</h4>
                    <p class="text-gray-600">************</p>
                  </div>
                </div>
                
                <div class="flex items-start">
                  <div class="text-primary text-xl mt-1 mr-4">
                    <i class="fa fa-clock-o"></i>
                  </div>
                  <div>
                    <h4 class="font-medium text-dark">工作时间</h4>
                    <p class="text-gray-600">周一至周五: 9:00 - 18:00</p>
                  </div>
                </div>
              </div>
              
              <div class="mt-8">
                <h4 class="font-bold text-dark mb-3">关注我们</h4>
                <div class="flex space-x-4">
                  <a href="#" class="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center text-primary hover:bg-primary hover:text-white transition-colors">
                    <i class="fa fa-weixin"></i>
                  </a>
                  <a href="#" class="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center text-primary hover:bg-primary hover:text-white transition-colors">
                    <i class="fa fa-weibo"></i>
                  </a>
                  <a href="#" class="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center text-primary hover:bg-primary hover:text-white transition-colors">
                    <i class="fa fa-linkedin"></i>
                  </a>
                </div>
              </div>
            </div>
            
            <div>
              <h3 class="font-bold text-dark text-lg mb-4">发送消息</h3>
              <form>
                <div class="mb-4">
                  <label for="name" class="block text-sm font-medium text-gray-700 mb-1">姓名</label>
                  <input type="text" id="name" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary transition-colors" placeholder="请输入您的姓名">
                </div>
                
                <div class="mb-4">
                  <label for="email" class="block text-sm font-medium text-gray-700 mb-1">邮箱</label>
                  <input type="email" id="email" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary transition-colors" placeholder="请输入您的邮箱">
                </div>
                
                <div class="mb-4">
                  <label for="subject" class="block text-sm font-medium text-gray-700 mb-1">主题</label>
                  <select id="subject" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary transition-colors">
                    <option value="">请选择咨询主题</option>
                    <option value="training">销售培训</option>
                    <option value="resources">资源获取</option>
                    <option value="process">流程咨询</option>
                    <option value="other">其他问题</option>
                  </select>
                </div>
                
                <div class="mb-6">
                  <label for="message" class="block text-sm font-medium text-gray-700 mb-1">消息内容</label>
                  <textarea id="message" rows="4" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary transition-colors" placeholder="请输入您的消息内容"></textarea>
                </div>
                
                <button type="submit" class="w-full bg-primary text-white font-medium py-3 px-4 rounded-lg hover:bg-primary/90 transition-colors shadow-md hover:shadow-lg transform hover:-translate-y-0.5 duration-300">
                  发送消息 <i class="fa fa-paper-plane ml-2"></i>
                </button>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- 页脚 -->
  <footer class="bg-dark text-white py-12">
    <div class="container mx-auto px-4">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
        <div>
          <h3 class="text-xl font-bold mb-4 flex items-center">
            <div class="w-8 h-8 rounded-lg overflow-hidden bg-white shadow-sm mr-3">
              <img src="https://mmbiz.qpic.cn/mmbiz_png/Jq3LZAUQiaSbx5VFxNJTIRy8YXsbtP71kcFl7iagCx7qLICSlE93CW4H4iaOfTiaKiaBFFEZ6NKtnLibE5T71nf55iarw/640?wx_fmt=png&from=appmsg" alt="鑫淼·翼企办" class="w-full h-full object-contain">
            </div>
            鑫淼·翼企办
          </h3>
          <p class="text-gray-400 mb-4">专业的企业服务平台，为企业提供全方位的数字化解决方案。</p>
          <div class="flex space-x-4">
            <a href="#" class="text-gray-400 hover:text-white transition-colors">
              <i class="fa fa-weixin"></i>
            </a>
            <a href="#" class="text-gray-400 hover:text-white transition-colors">
              <i class="fa fa-weibo"></i>
            </a>
            <a href="#" class="text-gray-400 hover:text-white transition-colors">
              <i class="fa fa-linkedin"></i>
            </a>
            <a href="#" class="text-gray-400 hover:text-white transition-colors">
              <i class="fa fa-youtube-play"></i>
            </a>
          </div>
        </div>
        
        <div>
          <h4 class="font-bold mb-4">快速链接</h4>
          <ul class="space-y-2">
            <li><a href="#overview" class="text-gray-400 hover:text-white transition-colors">流程概览</a></li>
            <li><a href="#stages" class="text-gray-400 hover:text-white transition-colors">详细阶段</a></li>
            <li><a href="#resources" class="text-gray-400 hover:text-white transition-colors">销售资源</a></li>
            <li><a href="#contact" class="text-gray-400 hover:text-white transition-colors">联系我们</a></li>
          </ul>
        </div>
        
        <div>
          <h4 class="font-bold mb-4">解决方案</h4>
          <ul class="space-y-2">
            <li><a href="#" class="text-gray-400 hover:text-white transition-colors">数字化转型</a></li>
            <li><a href="#" class="text-gray-400 hover:text-white transition-colors">客户关系管理</a></li>
            <li><a href="#" class="text-gray-400 hover:text-white transition-colors">销售自动化</a></li>
            <li><a href="#" class="text-gray-400 hover:text-white transition-colors">数据分析</a></li>
          </ul>
        </div>
        
        <div>
          <h4 class="font-bold mb-4">联系信息</h4>
          <ul class="space-y-2">
            <li class="flex items-start">
              <i class="fa fa-map-marker text-primary mt-1 mr-3"></i>
              <span class="text-gray-400">北京市海淀区中关村科技园区8号楼</span>
            </li>
            <li class="flex items-start">
              <i class="fa fa-phone text-primary mt-1 mr-3"></i>
              <span class="text-gray-400">************</span>
            </li>
            <li class="flex items-start">
              <i class="fa fa-envelope-o text-primary mt-1 mr-3"></i>
              <span class="text-gray-400"><EMAIL></span>
            </li>
          </ul>
        </div>
      </div>
      
      <div class="border-t border-gray-800 mt-10 pt-6 flex flex-col md:flex-row justify-between items-center">
        <p class="text-gray-500 text-sm">© 2025 鑫淼·翼企办. 保留所有权利.</p>
        <div class="flex space-x-6 mt-4 md:mt-0">
          <a href="#" class="text-gray-500 hover:text-gray-300 text-sm">隐私政策</a>
          <a href="#" class="text-gray-500 hover:text-gray-300 text-sm">服务条款</a>
          <a href="#" class="text-gray-500 hover:text-gray-300 text-sm">法律声明</a>
        </div>
      </div>
    </div>
  </footer>

  <!-- 浮动快速导航 -->
  <div id="floating-nav" class="fixed right-6 top-1/2 transform -translate-y-1/2 z-floating hidden lg:block">
    <div class="bg-white rounded-full shadow-lg border border-gray-200 p-2">
      <div class="flex flex-col space-y-2">
        <button class="nav-dot w-3 h-3 rounded-full bg-gray-300 hover:bg-primary transition-colors" data-target="overview" title="流程概览"></button>
        <button class="nav-dot w-3 h-3 rounded-full bg-gray-300 hover:bg-primary transition-colors" data-target="stages" title="详细阶段"></button>
        <button class="nav-dot w-3 h-3 rounded-full bg-gray-300 hover:bg-primary transition-colors" data-target="resources" title="销售资源"></button>
        <button class="nav-dot w-3 h-3 rounded-full bg-gray-300 hover:bg-primary transition-colors" data-target="contact" title="联系我们"></button>
      </div>
    </div>
  </div>

  <!-- 返回顶部按钮 -->
  <button id="back-to-top" class="fixed bottom-6 right-6 w-12 h-12 bg-primary text-white rounded-full shadow-lg hover:bg-primary/90 transition-all transform translate-y-16 opacity-0 z-floating">
    <i class="fa fa-arrow-up"></i>
  </button>

  <!-- KPI计算器模态框 -->
  <div id="kpi-modal" class="fixed inset-0 bg-black bg-opacity-50 z-modal hidden flex items-center justify-center p-4">
    <div class="bg-white rounded-xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
      <div class="p-6 border-b border-gray-200">
        <div class="flex items-center justify-between">
          <h3 class="text-xl font-bold text-dark">销售KPI计算器</h3>
          <button id="close-kpi-modal" class="text-gray-400 hover:text-gray-600 text-xl">
            <i class="fa fa-times"></i>
          </button>
        </div>
      </div>

      <div class="p-6">
        <form id="kpi-calculator-form">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">月度线索数量</label>
              <input type="number" id="monthly-leads" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary" placeholder="例如：100">
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">电话接通率 (%)</label>
              <input type="number" id="call-connect-rate" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary" placeholder="例如：40" value="40">
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">电话-微信转化率 (%)</label>
              <input type="number" id="phone-wechat-rate" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary" placeholder="目标：60" value="60">
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">微信-演示转化率 (%)</label>
              <input type="number" id="wechat-demo-rate" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary" placeholder="目标：30" value="30">
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">演示-报价转化率 (%)</label>
              <input type="number" id="demo-quote-rate" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary" placeholder="例如：70" value="70">
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">报价-签约转化率 (%)</label>
              <input type="number" id="quote-close-rate" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary" placeholder="目标：25" value="25">
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">平均客单价 (元)</label>
              <input type="number" id="avg-deal-size" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary" placeholder="例如：50000">
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">销售人员数量</label>
              <input type="number" id="sales-team-size" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary" placeholder="例如：5">
            </div>
          </div>

          <button type="button" id="calculate-kpi" class="w-full bg-primary text-white font-medium py-3 px-4 rounded-lg hover:bg-primary/90 transition-colors mb-6">
            计算KPI指标 <i class="fa fa-calculator ml-2"></i>
          </button>
        </form>

        <!-- 计算结果 -->
        <div id="kpi-results" class="hidden">
          <h4 class="text-lg font-bold text-dark mb-4">计算结果</h4>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="bg-primary/5 rounded-lg p-4">
              <h5 class="font-medium text-dark mb-2">月度签约数量</h5>
              <div id="monthly-deals" class="text-2xl font-bold text-primary">-</div>
            </div>

            <div class="bg-secondary/5 rounded-lg p-4">
              <h5 class="font-medium text-dark mb-2">月度营收</h5>
              <div id="monthly-revenue" class="text-2xl font-bold text-secondary">-</div>
            </div>

            <div class="bg-success/5 rounded-lg p-4">
              <h5 class="font-medium text-dark mb-2">人均月度业绩</h5>
              <div id="per-person-revenue" class="text-2xl font-bold text-success">-</div>
            </div>

            <div class="bg-warning/5 rounded-lg p-4">
              <h5 class="font-medium text-dark mb-2">整体转化率</h5>
              <div id="overall-conversion" class="text-2xl font-bold text-warning">-</div>
            </div>
          </div>

          <div class="mt-6 p-4 bg-gray-50 rounded-lg">
            <h5 class="font-medium text-dark mb-2">优化建议</h5>
            <div id="optimization-suggestions" class="text-sm text-gray-600">
              <!-- 动态生成优化建议 -->
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 搜索模态框 -->
  <div id="search-modal" class="fixed inset-0 bg-black bg-opacity-50 z-modal hidden flex items-start justify-center pt-20 p-4">
    <div class="bg-white rounded-xl shadow-2xl max-w-2xl w-full">
      <div class="p-6">
        <div class="flex items-center mb-4">
          <i class="fa fa-search text-primary text-xl mr-3"></i>
          <input type="text" id="search-input" class="flex-1 text-lg border-none outline-none" placeholder="搜索销售流程、话术、工具...">
          <button id="close-search-modal" class="text-gray-400 hover:text-gray-600 text-xl ml-4">
            <i class="fa fa-times"></i>
          </button>
        </div>

        <div id="search-results" class="max-h-96 overflow-y-auto">
          <div class="text-gray-500 text-center py-8">
            输入关键词开始搜索...
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 进度跟踪器模态框 -->
  <div id="progress-modal" class="fixed inset-0 bg-black bg-opacity-50 z-modal hidden flex items-center justify-center p-4">
    <div class="bg-white rounded-xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
      <div class="p-6 border-b border-gray-200">
        <div class="flex items-center justify-between">
          <h3 class="text-xl font-bold text-dark">销售进度跟踪器</h3>
          <button id="close-progress-modal" class="text-gray-400 hover:text-gray-600 text-xl">
            <i class="fa fa-times"></i>
          </button>
        </div>
      </div>

      <div class="p-6">
        <!-- 客户信息输入 -->
        <div class="mb-6">
          <h4 class="font-bold text-dark mb-4">客户信息</h4>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <input type="text" id="customer-name" class="px-4 py-2 border border-gray-300 rounded-lg" placeholder="客户名称">
            <input type="text" id="customer-company" class="px-4 py-2 border border-gray-300 rounded-lg" placeholder="公司名称">
            <select id="customer-industry" class="px-4 py-2 border border-gray-300 rounded-lg">
              <option value="">选择行业</option>
              <option value="finance">金融催收/理财保险</option>
              <option value="realestate">地产家装</option>
              <option value="education">教育培训</option>
              <option value="other">其他</option>
            </select>
          </div>
        </div>

        <!-- 进度跟踪 -->
        <div class="mb-6">
          <h4 class="font-bold text-dark mb-4">销售进度</h4>
          <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div class="progress-stage" data-stage="1">
              <div class="flex items-center mb-2">
                <input type="checkbox" id="stage-1" class="mr-2">
                <label for="stage-1" class="font-medium">线索获取</label>
              </div>
              <div class="text-sm text-gray-600">BANT评分、线索分级</div>
            </div>

            <div class="progress-stage" data-stage="2">
              <div class="flex items-center mb-2">
                <input type="checkbox" id="stage-2" class="mr-2">
                <label for="stage-2" class="font-medium">初步接触</label>
              </div>
              <div class="text-sm text-gray-600">建立信任、获取微信</div>
            </div>

            <div class="progress-stage" data-stage="3">
              <div class="flex items-center mb-2">
                <input type="checkbox" id="stage-3" class="mr-2">
                <label for="stage-3" class="font-medium">需求挖掘</label>
              </div>
              <div class="text-sm text-gray-600">SPIN提问、痛点分析</div>
            </div>

            <div class="progress-stage" data-stage="4">
              <div class="flex items-center mb-2">
                <input type="checkbox" id="stage-4" class="mr-2">
                <label for="stage-4" class="font-medium">邀约演示</label>
              </div>
              <div class="text-sm text-gray-600">场景化演示邀约</div>
            </div>

            <div class="progress-stage" data-stage="5">
              <div class="flex items-center mb-2">
                <input type="checkbox" id="stage-5" class="mr-2">
                <label for="stage-5" class="font-medium">方案演示</label>
              </div>
              <div class="text-sm text-gray-600">价值展示、ROI计算</div>
            </div>

            <div class="progress-stage" data-stage="6">
              <div class="flex items-center mb-2">
                <input type="checkbox" id="stage-6" class="mr-2">
                <label for="stage-6" class="font-medium">异议处理</label>
              </div>
              <div class="text-sm text-gray-600">LAER框架应对</div>
            </div>

            <div class="progress-stage" data-stage="7">
              <div class="flex items-center mb-2">
                <input type="checkbox" id="stage-7" class="mr-2">
                <label for="stage-7" class="font-medium">促成交易</label>
              </div>
              <div class="text-sm text-gray-600">成交信号、签约</div>
            </div>

            <div class="progress-stage" data-stage="8">
              <div class="flex items-center mb-2">
                <input type="checkbox" id="stage-8" class="mr-2">
                <label for="stage-8" class="font-medium">客户成功</label>
              </div>
              <div class="text-sm text-gray-600">交付、续费、推荐</div>
            </div>
          </div>
        </div>

        <!-- 备注和下一步 -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">备注</label>
            <textarea id="progress-notes" rows="4" class="w-full px-4 py-2 border border-gray-300 rounded-lg" placeholder="记录重要信息、客户反馈等..."></textarea>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">下一步行动</label>
            <textarea id="next-actions" rows="4" class="w-full px-4 py-2 border border-gray-300 rounded-lg" placeholder="计划的下一步行动和时间安排..."></textarea>
          </div>
        </div>

        <div class="flex justify-end mt-6 space-x-4">
          <button id="save-progress" class="bg-primary text-white px-6 py-2 rounded-lg hover:bg-primary/90 transition-colors">
            保存进度 <i class="fa fa-save ml-2"></i>
          </button>
          <button id="export-progress" class="bg-secondary text-white px-6 py-2 rounded-lg hover:bg-secondary/90 transition-colors">
            导出报告 <i class="fa fa-download ml-2"></i>
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- 话术训练模态框 -->
  <div id="training-modal" class="fixed inset-0 bg-black bg-opacity-50 z-modal hidden flex items-center justify-center p-4">
    <div class="bg-white rounded-xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
      <div class="p-6 border-b border-gray-200">
        <div class="flex items-center justify-between">
          <h3 class="text-xl font-bold text-dark">销售话术训练中心</h3>
          <button id="close-training-modal" class="text-gray-400 hover:text-gray-600 text-xl">
            <i class="fa fa-times"></i>
          </button>
        </div>
      </div>

      <div class="p-6">
        <!-- 训练模式选择 -->
        <div class="mb-6">
          <h4 class="font-bold text-dark mb-4">选择训练模式</h4>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <button class="training-mode-btn p-4 border-2 border-gray-200 rounded-lg hover:border-primary transition-colors" data-mode="scenario">
              <div class="text-center">
                <i class="fa fa-comments text-2xl text-primary mb-2"></i>
                <h5 class="font-medium text-dark">情景对话</h5>
                <p class="text-sm text-gray-600">模拟真实销售场景</p>
              </div>
            </button>

            <button class="training-mode-btn p-4 border-2 border-gray-200 rounded-lg hover:border-primary transition-colors" data-mode="objection">
              <div class="text-center">
                <i class="fa fa-shield text-2xl text-secondary mb-2"></i>
                <h5 class="font-medium text-dark">异议处理</h5>
                <p class="text-sm text-gray-600">练习异议应对技巧</p>
              </div>
            </button>

            <button class="training-mode-btn p-4 border-2 border-gray-200 rounded-lg hover:border-primary transition-colors" data-mode="closing">
              <div class="text-center">
                <i class="fa fa-handshake-o text-2xl text-success mb-2"></i>
                <h5 class="font-medium text-dark">成交技巧</h5>
                <p class="text-sm text-gray-600">掌握成交话术</p>
              </div>
            </button>
          </div>
        </div>

        <!-- 训练内容区域 -->
        <div id="training-content" class="hidden">
          <!-- 情景对话训练 -->
          <div id="scenario-training" class="training-section hidden">
            <h4 class="font-bold text-dark mb-4">情景对话训练</h4>
            <div class="bg-gray-50 rounded-lg p-6">
              <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">选择客户类型</label>
                <select id="customer-type" class="w-full px-4 py-2 border border-gray-300 rounded-lg">
                  <option value="finance">金融行业客户</option>
                  <option value="realestate">地产行业客户</option>
                  <option value="education">教育行业客户</option>
                  <option value="skeptical">怀疑型客户</option>
                  <option value="analytical">分析型客户</option>
                  <option value="decisive">决断型客户</option>
                </select>
              </div>

              <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">选择销售阶段</label>
                <select id="sales-stage" class="w-full px-4 py-2 border border-gray-300 rounded-lg">
                  <option value="1">线索获取阶段</option>
                  <option value="2">初步接触阶段</option>
                  <option value="3">需求挖掘阶段</option>
                  <option value="4">邀约演示阶段</option>
                  <option value="5">方案演示阶段</option>
                  <option value="6">异议处理阶段</option>
                  <option value="7">促成交易阶段</option>
                </select>
              </div>

              <button id="start-scenario" class="bg-primary text-white px-6 py-2 rounded-lg hover:bg-primary/90 transition-colors">
                开始情景训练 <i class="fa fa-play ml-2"></i>
              </button>
            </div>

            <!-- 对话区域 -->
            <div id="dialogue-area" class="hidden mt-6">
              <div class="bg-white rounded-lg border p-6">
                <div id="dialogue-content" class="space-y-4 mb-4 max-h-96 overflow-y-auto">
                  <!-- 对话内容将在这里动态生成 -->
                </div>

                <div class="border-t pt-4">
                  <div class="flex space-x-4">
                    <input type="text" id="user-response" class="flex-1 px-4 py-2 border border-gray-300 rounded-lg" placeholder="输入您的回应...">
                    <button id="send-response" class="bg-primary text-white px-6 py-2 rounded-lg hover:bg-primary/90 transition-colors">
                      发送 <i class="fa fa-paper-plane ml-2"></i>
                    </button>
                  </div>

                  <div class="mt-4 flex space-x-2">
                    <button id="get-hint" class="bg-secondary text-white px-4 py-2 rounded-lg hover:bg-secondary/90 transition-colors text-sm">
                      获取提示 <i class="fa fa-lightbulb-o ml-1"></i>
                    </button>
                    <button id="show-best-practice" class="bg-success text-white px-4 py-2 rounded-lg hover:bg-success/90 transition-colors text-sm">
                      查看最佳实践 <i class="fa fa-star ml-1"></i>
                    </button>
                    <button id="restart-scenario" class="bg-warning text-white px-4 py-2 rounded-lg hover:bg-warning/90 transition-colors text-sm">
                      重新开始 <i class="fa fa-refresh ml-1"></i>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 异议处理训练 -->
          <div id="objection-training" class="training-section hidden">
            <h4 class="font-bold text-dark mb-4">异议处理训练</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div class="bg-gray-50 rounded-lg p-6">
                <h5 class="font-medium text-dark mb-4">常见异议类型</h5>
                <div class="space-y-3">
                  <button class="objection-btn w-full text-left p-3 bg-white rounded border hover:border-primary transition-colors" data-objection="price">
                    <div class="font-medium">价格异议</div>
                    <div class="text-sm text-gray-600">"你们的价格太贵了"</div>
                  </button>

                  <button class="objection-btn w-full text-left p-3 bg-white rounded border hover:border-primary transition-colors" data-objection="trust">
                    <div class="font-medium">信任异议</div>
                    <div class="text-sm text-gray-600">"我需要考虑一下"</div>
                  </button>

                  <button class="objection-btn w-full text-left p-3 bg-white rounded border hover:border-primary transition-colors" data-objection="authority">
                    <div class="font-medium">权限异议</div>
                    <div class="text-sm text-gray-600">"我需要和老板商量"</div>
                  </button>

                  <button class="objection-btn w-full text-left p-3 bg-white rounded border hover:border-primary transition-colors" data-objection="timing">
                    <div class="font-medium">时机异议</div>
                    <div class="text-sm text-gray-600">"现在不是合适的时机"</div>
                  </button>

                  <button class="objection-btn w-full text-left p-3 bg-white rounded border hover:border-primary transition-colors" data-objection="competitor">
                    <div class="font-medium">竞品异议</div>
                    <div class="text-sm text-gray-600">"我们在看其他供应商"</div>
                  </button>
                </div>
              </div>

              <div class="bg-white rounded-lg border p-6">
                <h5 class="font-medium text-dark mb-4">LAER处理框架</h5>
                <div id="objection-response" class="space-y-4">
                  <div class="text-center text-gray-500 py-8">
                    选择左侧异议类型开始练习
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 成交技巧训练 -->
          <div id="closing-training" class="training-section hidden">
            <h4 class="font-bold text-dark mb-4">成交技巧训练</h4>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div class="bg-primary/5 rounded-lg p-6">
                <h5 class="font-medium text-dark mb-3">假设性成交</h5>
                <p class="text-sm text-gray-600 mb-4">以假设成交为前提推进后续环节</p>
                <button class="closing-technique-btn bg-primary text-white px-4 py-2 rounded text-sm hover:bg-primary/90 transition-colors" data-technique="assumptive">
                  开始练习 <i class="fa fa-play ml-1"></i>
                </button>
              </div>

              <div class="bg-secondary/5 rounded-lg p-6">
                <h5 class="font-medium text-dark mb-3">选择性成交</h5>
                <p class="text-sm text-gray-600 mb-4">提供两个肯定性选择</p>
                <button class="closing-technique-btn bg-secondary text-white px-4 py-2 rounded text-sm hover:bg-secondary/90 transition-colors" data-technique="alternative">
                  开始练习 <i class="fa fa-play ml-1"></i>
                </button>
              </div>

              <div class="bg-success/5 rounded-lg p-6">
                <h5 class="font-medium text-dark mb-3">紧迫感成交</h5>
                <p class="text-sm text-gray-600 mb-4">创造合理的时间压力</p>
                <button class="closing-technique-btn bg-success text-white px-4 py-2 rounded text-sm hover:bg-success/90 transition-colors" data-technique="urgency">
                  开始练习 <i class="fa fa-play ml-1"></i>
                </button>
              </div>
            </div>

            <div id="closing-practice" class="hidden mt-6 bg-white rounded-lg border p-6">
              <div id="closing-content">
                <!-- 成交练习内容 -->
              </div>
            </div>
          </div>
        </div>

        <!-- 训练统计 -->
        <div id="training-stats" class="hidden mt-6 bg-gray-50 rounded-lg p-6">
          <h4 class="font-bold text-dark mb-4">训练统计</h4>
          <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div class="text-center">
              <div class="text-2xl font-bold text-primary" id="total-sessions">0</div>
              <div class="text-sm text-gray-600">总训练次数</div>
            </div>
            <div class="text-center">
              <div class="text-2xl font-bold text-secondary" id="success-rate">0%</div>
              <div class="text-sm text-gray-600">成功率</div>
            </div>
            <div class="text-center">
              <div class="text-2xl font-bold text-success" id="best-score">0</div>
              <div class="text-sm text-gray-600">最高得分</div>
            </div>
            <div class="text-center">
              <div class="text-2xl font-bold text-warning" id="training-time">0</div>
              <div class="text-sm text-gray-600">训练时长(分钟)</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 数据分析仪表板模态框 -->
  <div id="analytics-modal" class="fixed inset-0 bg-black bg-opacity-50 z-modal hidden flex items-center justify-center p-4">
    <div class="bg-white rounded-xl shadow-2xl max-w-6xl w-full max-h-[90vh] overflow-y-auto">
      <div class="p-6 border-b border-gray-200">
        <div class="flex items-center justify-between">
          <h3 class="text-xl font-bold text-dark">销售数据分析仪表板</h3>
          <div class="flex items-center space-x-4">
            <select id="time-range" class="px-3 py-1 border border-gray-300 rounded text-sm">
              <option value="7">最近7天</option>
              <option value="30">最近30天</option>
              <option value="90">最近90天</option>
              <option value="365">最近一年</option>
            </select>
            <button id="refresh-data" class="text-primary hover:text-primary/80">
              <i class="fa fa-refresh"></i>
            </button>
            <button id="close-analytics-modal" class="text-gray-400 hover:text-gray-600 text-xl">
              <i class="fa fa-times"></i>
            </button>
          </div>
        </div>
      </div>

      <div class="p-6">
        <!-- 关键指标概览 -->
        <div class="grid grid-cols-2 md:grid-cols-4 gap-6 mb-8">
          <div class="bg-primary/5 rounded-lg p-6 text-center">
            <div class="text-3xl font-bold text-primary mb-2" id="total-leads">0</div>
            <div class="text-sm text-gray-600">总线索数</div>
            <div class="text-xs text-green-600 mt-1" id="leads-trend">+0%</div>
          </div>

          <div class="bg-secondary/5 rounded-lg p-6 text-center">
            <div class="text-3xl font-bold text-secondary mb-2" id="conversion-rate">0%</div>
            <div class="text-sm text-gray-600">整体转化率</div>
            <div class="text-xs text-green-600 mt-1" id="conversion-trend">+0%</div>
          </div>

          <div class="bg-success/5 rounded-lg p-6 text-center">
            <div class="text-3xl font-bold text-success mb-2" id="total-revenue">¥0</div>
            <div class="text-sm text-gray-600">总营收</div>
            <div class="text-xs text-green-600 mt-1" id="revenue-trend">+0%</div>
          </div>

          <div class="bg-warning/5 rounded-lg p-6 text-center">
            <div class="text-3xl font-bold text-warning mb-2" id="avg-cycle">0</div>
            <div class="text-sm text-gray-600">平均销售周期(天)</div>
            <div class="text-xs text-red-600 mt-1" id="cycle-trend">+0%</div>
          </div>
        </div>

        <!-- 销售漏斗分析 -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          <div class="bg-white rounded-lg border p-6">
            <h4 class="font-bold text-dark mb-4">销售漏斗分析</h4>
            <div class="space-y-4">
              <div class="funnel-stage">
                <div class="flex justify-between items-center mb-2">
                  <span class="text-sm font-medium">线索获取</span>
                  <span class="text-sm text-gray-600" id="funnel-leads">0</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-3">
                  <div class="bg-primary h-3 rounded-full" style="width: 100%" id="funnel-leads-bar"></div>
                </div>
              </div>

              <div class="funnel-stage">
                <div class="flex justify-between items-center mb-2">
                  <span class="text-sm font-medium">初步接触</span>
                  <span class="text-sm text-gray-600" id="funnel-contact">0</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-3">
                  <div class="bg-secondary h-3 rounded-full" style="width: 0%" id="funnel-contact-bar"></div>
                </div>
              </div>

              <div class="funnel-stage">
                <div class="flex justify-between items-center mb-2">
                  <span class="text-sm font-medium">需求挖掘</span>
                  <span class="text-sm text-gray-600" id="funnel-needs">0</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-3">
                  <div class="bg-success h-3 rounded-full" style="width: 0%" id="funnel-needs-bar"></div>
                </div>
              </div>

              <div class="funnel-stage">
                <div class="flex justify-between items-center mb-2">
                  <span class="text-sm font-medium">方案演示</span>
                  <span class="text-sm text-gray-600" id="funnel-demo">0</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-3">
                  <div class="bg-warning h-3 rounded-full" style="width: 0%" id="funnel-demo-bar"></div>
                </div>
              </div>

              <div class="funnel-stage">
                <div class="flex justify-between items-center mb-2">
                  <span class="text-sm font-medium">成功签约</span>
                  <span class="text-sm text-gray-600" id="funnel-closed">0</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-3">
                  <div class="bg-danger h-3 rounded-full" style="width: 0%" id="funnel-closed-bar"></div>
                </div>
              </div>
            </div>
          </div>

          <!-- 转化率分析 -->
          <div class="bg-white rounded-lg border p-6">
            <h4 class="font-bold text-dark mb-4">各阶段转化率</h4>
            <div class="space-y-4">
              <div class="flex justify-between items-center p-3 bg-gray-50 rounded">
                <span class="text-sm font-medium">线索→接触</span>
                <div class="flex items-center space-x-2">
                  <span class="text-sm font-bold" id="conv-lead-contact">0%</span>
                  <span class="text-xs text-gray-500">目标: 60%</span>
                </div>
              </div>

              <div class="flex justify-between items-center p-3 bg-gray-50 rounded">
                <span class="text-sm font-medium">接触→挖掘</span>
                <div class="flex items-center space-x-2">
                  <span class="text-sm font-bold" id="conv-contact-needs">0%</span>
                  <span class="text-xs text-gray-500">目标: 30%</span>
                </div>
              </div>

              <div class="flex justify-between items-center p-3 bg-gray-50 rounded">
                <span class="text-sm font-medium">挖掘→演示</span>
                <div class="flex items-center space-x-2">
                  <span class="text-sm font-bold" id="conv-needs-demo">0%</span>
                  <span class="text-xs text-gray-500">目标: 70%</span>
                </div>
              </div>

              <div class="flex justify-between items-center p-3 bg-gray-50 rounded">
                <span class="text-sm font-medium">演示→签约</span>
                <div class="flex items-center space-x-2">
                  <span class="text-sm font-bold" id="conv-demo-closed">0%</span>
                  <span class="text-xs text-gray-500">目标: 25%</span>
                </div>
              </div>

              <div class="border-t pt-3 mt-4">
                <div class="flex justify-between items-center p-3 bg-primary/5 rounded">
                  <span class="text-sm font-bold">整体转化率</span>
                  <span class="text-lg font-bold text-primary" id="overall-conv">0%</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 销售团队表现 -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          <div class="bg-white rounded-lg border p-6">
            <h4 class="font-bold text-dark mb-4">销售团队表现</h4>
            <div class="space-y-4" id="team-performance">
              <!-- 动态生成销售人员数据 -->
            </div>
          </div>

          <!-- 客户来源分析 -->
          <div class="bg-white rounded-lg border p-6">
            <h4 class="font-bold text-dark mb-4">客户来源分析</h4>
            <div class="space-y-4">
              <div class="flex justify-between items-center">
                <span class="text-sm">官网/广告</span>
                <div class="flex items-center space-x-2">
                  <div class="w-24 bg-gray-200 rounded-full h-2">
                    <div class="bg-primary h-2 rounded-full" style="width: 45%" id="source-website-bar"></div>
                  </div>
                  <span class="text-sm font-medium" id="source-website">45%</span>
                </div>
              </div>

              <div class="flex justify-between items-center">
                <span class="text-sm">抖音/短视频</span>
                <div class="flex items-center space-x-2">
                  <div class="w-24 bg-gray-200 rounded-full h-2">
                    <div class="bg-secondary h-2 rounded-full" style="width: 30%" id="source-social-bar"></div>
                  </div>
                  <span class="text-sm font-medium" id="source-social">30%</span>
                </div>
              </div>

              <div class="flex justify-between items-center">
                <span class="text-sm">客户推荐</span>
                <div class="flex items-center space-x-2">
                  <div class="w-24 bg-gray-200 rounded-full h-2">
                    <div class="bg-success h-2 rounded-full" style="width: 15%" id="source-referral-bar"></div>
                  </div>
                  <span class="text-sm font-medium" id="source-referral">15%</span>
                </div>
              </div>

              <div class="flex justify-between items-center">
                <span class="text-sm">其他渠道</span>
                <div class="flex items-center space-x-2">
                  <div class="w-24 bg-gray-200 rounded-full h-2">
                    <div class="bg-warning h-2 rounded-full" style="width: 10%" id="source-other-bar"></div>
                  </div>
                  <span class="text-sm font-medium" id="source-other">10%</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 趋势分析 -->
        <div class="bg-white rounded-lg border p-6">
          <h4 class="font-bold text-dark mb-4">趋势分析</h4>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div class="text-center">
              <div class="text-2xl font-bold text-primary mb-2">📈</div>
              <div class="text-sm font-medium text-dark">线索质量提升</div>
              <div class="text-xs text-gray-600">BANT评分平均提高15%</div>
            </div>

            <div class="text-center">
              <div class="text-2xl font-bold text-success mb-2">🎯</div>
              <div class="text-sm font-medium text-dark">转化率优化</div>
              <div class="text-xs text-gray-600">整体转化率提升8%</div>
            </div>

            <div class="text-center">
              <div class="text-2xl font-bold text-warning mb-2">⚡</div>
              <div class="text-sm font-medium text-dark">销售周期缩短</div>
              <div class="text-xs text-gray-600">平均周期减少5天</div>
            </div>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="flex justify-end mt-6 space-x-4">
          <button id="export-analytics" class="bg-secondary text-white px-6 py-2 rounded-lg hover:bg-secondary/90 transition-colors">
            导出报告 <i class="fa fa-download ml-2"></i>
          </button>
          <button id="schedule-report" class="bg-success text-white px-6 py-2 rounded-lg hover:bg-success/90 transition-colors">
            定期报告 <i class="fa fa-calendar ml-2"></i>
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- 帮助系统模态框 -->
  <div id="help-modal" class="fixed inset-0 bg-black bg-opacity-50 z-modal hidden flex items-center justify-center p-4">
    <div class="bg-white rounded-xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
      <div class="p-6 border-b border-gray-200">
        <div class="flex items-center justify-between">
          <h3 class="text-xl font-bold text-dark">帮助中心</h3>
          <button id="close-help-modal" class="text-gray-400 hover:text-gray-600 text-xl">
            <i class="fa fa-times"></i>
          </button>
        </div>
      </div>

      <div class="p-6">
        <!-- 帮助导航 -->
        <div class="flex flex-wrap gap-2 mb-6">
          <button class="help-tab-btn px-4 py-2 rounded-lg bg-primary text-white" data-tab="shortcuts">
            快捷键
          </button>
          <button class="help-tab-btn px-4 py-2 rounded-lg bg-gray-200 text-gray-700" data-tab="features">
            功能介绍
          </button>
          <button class="help-tab-btn px-4 py-2 rounded-lg bg-gray-200 text-gray-700" data-tab="tips">
            使用技巧
          </button>
          <button class="help-tab-btn px-4 py-2 rounded-lg bg-gray-200 text-gray-700" data-tab="faq">
            常见问题
          </button>
        </div>

        <!-- 快捷键指南 -->
        <div id="help-shortcuts" class="help-content">
          <h4 class="font-bold text-dark mb-4">键盘快捷键</h4>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="space-y-3">
              <h5 class="font-medium text-dark mb-3">导航快捷键</h5>
              <div class="flex justify-between items-center p-3 bg-gray-50 rounded">
                <span class="text-sm">打开搜索</span>
                <kbd class="px-2 py-1 bg-gray-200 rounded text-xs">Ctrl + K</kbd>
              </div>
              <div class="flex justify-between items-center p-3 bg-gray-50 rounded">
                <span class="text-sm">关闭模态框</span>
                <kbd class="px-2 py-1 bg-gray-200 rounded text-xs">Esc</kbd>
              </div>
              <div class="flex justify-between items-center p-3 bg-gray-50 rounded">
                <span class="text-sm">返回顶部</span>
                <kbd class="px-2 py-1 bg-gray-200 rounded text-xs">Home</kbd>
              </div>
            </div>

            <div class="space-y-3">
              <h5 class="font-medium text-dark mb-3">功能快捷键</h5>
              <div class="flex justify-between items-center p-3 bg-gray-50 rounded">
                <span class="text-sm">发送消息</span>
                <kbd class="px-2 py-1 bg-gray-200 rounded text-xs">Enter</kbd>
              </div>
              <div class="flex justify-between items-center p-3 bg-gray-50 rounded">
                <span class="text-sm">保存进度</span>
                <kbd class="px-2 py-1 bg-gray-200 rounded text-xs">Ctrl + S</kbd>
              </div>
              <div class="flex justify-between items-center p-3 bg-gray-50 rounded">
                <span class="text-sm">刷新数据</span>
                <kbd class="px-2 py-1 bg-gray-200 rounded text-xs">F5</kbd>
              </div>
            </div>
          </div>
        </div>

        <!-- 功能介绍 -->
        <div id="help-features" class="help-content hidden">
          <h4 class="font-bold text-dark mb-4">功能介绍</h4>
          <div class="space-y-6">
            <div class="border rounded-lg p-4">
              <h5 class="font-medium text-dark mb-2 flex items-center">
                <i class="fa fa-search text-primary mr-2"></i>
                智能搜索
              </h5>
              <p class="text-sm text-gray-600">快速搜索销售流程、话术模板、工具资源等内容，支持关键词匹配和实时结果展示。</p>
            </div>

            <div class="border rounded-lg p-4">
              <h5 class="font-medium text-dark mb-2 flex items-center">
                <i class="fa fa-calculator text-secondary mr-2"></i>
                KPI计算器
              </h5>
              <p class="text-sm text-gray-600">输入销售数据，自动计算转化率、营收预测等关键指标，并提供优化建议。</p>
            </div>

            <div class="border rounded-lg p-4">
              <h5 class="font-medium text-dark mb-2 flex items-center">
                <i class="fa fa-tasks text-success mr-2"></i>
                进度跟踪器
              </h5>
              <p class="text-sm text-gray-600">跟踪客户在销售流程中的进度，记录重要信息，生成进度报告。</p>
            </div>

            <div class="border rounded-lg p-4">
              <h5 class="font-medium text-dark mb-2 flex items-center">
                <i class="fa fa-graduation-cap text-warning mr-2"></i>
                话术训练
              </h5>
              <p class="text-sm text-gray-600">提供情景对话、异议处理、成交技巧等多种训练模式，提升销售技能。</p>
            </div>

            <div class="border rounded-lg p-4">
              <h5 class="font-medium text-dark mb-2 flex items-center">
                <i class="fa fa-bar-chart text-danger mr-2"></i>
                数据分析
              </h5>
              <p class="text-sm text-gray-600">全面的销售数据分析，包括漏斗分析、转化率统计、团队表现等。</p>
            </div>
          </div>
        </div>

        <!-- 使用技巧 -->
        <div id="help-tips" class="help-content hidden">
          <h4 class="font-bold text-dark mb-4">使用技巧</h4>
          <div class="space-y-4">
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h5 class="font-medium text-blue-800 mb-2">💡 高效使用建议</h5>
              <ul class="text-sm text-blue-700 space-y-1">
                <li>• 每天开始工作前，先查看销售流程概览，明确当天重点</li>
                <li>• 使用进度跟踪器记录每个客户的详细信息和跟进计划</li>
                <li>• 定期使用KPI计算器分析自己的销售表现</li>
                <li>• 遇到异议时，及时查阅异议处理话术库</li>
              </ul>
            </div>

            <div class="bg-green-50 border border-green-200 rounded-lg p-4">
              <h5 class="font-medium text-green-800 mb-2">🎯 最佳实践</h5>
              <ul class="text-sm text-green-700 space-y-1">
                <li>• 严格按照8阶段销售流程执行，不跳跃阶段</li>
                <li>• 每次客户沟通后，立即更新进度跟踪器</li>
                <li>• 定期参与话术训练，保持销售技能敏锐度</li>
                <li>• 利用数据分析功能，找出自己的薄弱环节</li>
              </ul>
            </div>

            <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <h5 class="font-medium text-yellow-800 mb-2">⚠️ 注意事项</h5>
              <ul class="text-sm text-yellow-700 space-y-1">
                <li>• 数据会自动保存到本地，但建议定期导出备份</li>
                <li>• 话术模板仅供参考，需要根据实际情况灵活调整</li>
                <li>• 进度跟踪器中的敏感信息请妥善保管</li>
                <li>• 定期清理浏览器缓存可能会丢失本地数据</li>
              </ul>
            </div>
          </div>
        </div>

        <!-- 常见问题 -->
        <div id="help-faq" class="help-content hidden">
          <h4 class="font-bold text-dark mb-4">常见问题</h4>
          <div class="space-y-4">
            <div class="border rounded-lg p-4">
              <h5 class="font-medium text-dark mb-2">Q: 数据会丢失吗？</h5>
              <p class="text-sm text-gray-600">A: 数据保存在浏览器本地存储中，除非主动清除或更换设备，否则不会丢失。建议定期导出重要数据。</p>
            </div>

            <div class="border rounded-lg p-4">
              <h5 class="font-medium text-dark mb-2">Q: 可以在手机上使用吗？</h5>
              <p class="text-sm text-gray-600">A: 完全支持！页面采用响应式设计，在手机、平板、电脑上都能正常使用。</p>
            </div>

            <div class="border rounded-lg p-4">
              <h5 class="font-medium text-dark mb-2">Q: 话术训练的评分标准是什么？</h5>
              <p class="text-sm text-gray-600">A: 评分基于回应的相关性、专业性和完整性。建议多练习，参考最佳实践案例。</p>
            </div>

            <div class="border rounded-lg p-4">
              <h5 class="font-medium text-dark mb-2">Q: 如何获得更多帮助？</h5>
              <p class="text-sm text-gray-600">A: 可以联系我们的客服团队，或者查看详细的用户手册和视频教程。</p>
            </div>
          </div>
        </div>

        <!-- 联系支持 -->
        <div class="mt-8 p-4 bg-gray-50 rounded-lg">
          <h5 class="font-medium text-dark mb-2">需要更多帮助？</h5>
          <p class="text-sm text-gray-600 mb-3">如果您有其他问题或建议，欢迎联系我们：</p>
          <div class="flex flex-wrap gap-4">
            <a href="mailto:<EMAIL>" class="text-primary hover:underline text-sm">
              <i class="fa fa-envelope mr-1"></i> <EMAIL>
            </a>
            <a href="tel:************" class="text-primary hover:underline text-sm">
              <i class="fa fa-phone mr-1"></i> ************
            </a>
            <a href="#" class="text-primary hover:underline text-sm">
              <i class="fa fa-wechat mr-1"></i> 微信客服
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 技能评估模态框 -->
  <div id="assessment-modal" class="fixed inset-0 bg-black bg-opacity-50 z-modal hidden flex items-center justify-center p-4">
    <div class="bg-white rounded-xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
      <div class="p-6 border-b border-gray-200">
        <div class="flex items-center justify-between">
          <h3 class="text-xl font-bold text-dark">销售技能评估</h3>
          <button id="close-assessment-modal" class="text-gray-400 hover:text-gray-600 text-xl">
            <i class="fa fa-times"></i>
          </button>
        </div>
      </div>

      <div class="p-6">
        <!-- 评估介绍 -->
        <div id="assessment-intro" class="text-center mb-8">
          <div class="w-20 h-20 bg-primary/10 rounded-full flex items-center justify-center text-primary text-3xl mx-auto mb-4">
            <i class="fa fa-star"></i>
          </div>
          <h4 class="text-xl font-bold text-dark mb-4">销售技能全面评估</h4>
          <p class="text-gray-600 mb-6 max-w-2xl mx-auto">通过专业的销售技能评估，了解您在8个销售阶段的能力水平，获得个性化的学习建议和发展路径。</p>

          <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
            <div class="bg-primary/5 rounded-lg p-4">
              <div class="text-2xl font-bold text-primary mb-1">40</div>
              <div class="text-sm text-gray-600">专业题目</div>
            </div>
            <div class="bg-secondary/5 rounded-lg p-4">
              <div class="text-2xl font-bold text-secondary mb-1">15</div>
              <div class="text-sm text-gray-600">分钟完成</div>
            </div>
            <div class="bg-success/5 rounded-lg p-4">
              <div class="text-2xl font-bold text-success mb-1">8</div>
              <div class="text-sm text-gray-600">维度评估</div>
            </div>
          </div>

          <button id="start-assessment" class="bg-primary text-white px-8 py-3 rounded-lg font-medium hover:bg-primary/90 transition-colors">
            开始评估 <i class="fa fa-arrow-right ml-2"></i>
          </button>
        </div>

        <!-- 评估问题 -->
        <div id="assessment-questions" class="hidden">
          <div class="mb-6">
            <div class="flex justify-between items-center mb-4">
              <h4 class="font-bold text-dark">技能评估进行中</h4>
              <div class="text-sm text-gray-600">
                <span id="current-question">1</span> / <span id="total-questions">40</span>
              </div>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2">
              <div id="progress-bar" class="bg-primary h-2 rounded-full transition-all duration-300" style="width: 2.5%"></div>
            </div>
          </div>

          <div class="bg-gray-50 rounded-xl p-6 mb-6">
            <div class="mb-4">
              <span class="text-sm bg-primary/10 text-primary px-3 py-1 rounded-full" id="question-category">线索获取</span>
            </div>
            <h5 class="text-lg font-medium text-dark mb-4" id="question-text">
              在进行BANT评分时，以下哪个因素最能准确判断线索的优先级？
            </h5>
            <div class="space-y-3" id="question-options">
              <label class="flex items-start space-x-3 p-3 bg-white rounded-lg border hover:border-primary cursor-pointer transition-colors">
                <input type="radio" name="answer" value="A" class="mt-1">
                <span class="text-sm">预算充足程度</span>
              </label>
              <label class="flex items-start space-x-3 p-3 bg-white rounded-lg border hover:border-primary cursor-pointer transition-colors">
                <input type="radio" name="answer" value="B" class="mt-1">
                <span class="text-sm">决策权限级别</span>
              </label>
              <label class="flex items-start space-x-3 p-3 bg-white rounded-lg border hover:border-primary cursor-pointer transition-colors">
                <input type="radio" name="answer" value="C" class="mt-1">
                <span class="text-sm">需求紧迫程度</span>
              </label>
              <label class="flex items-start space-x-3 p-3 bg-white rounded-lg border hover:border-primary cursor-pointer transition-colors">
                <input type="radio" name="answer" value="D" class="mt-1">
                <span class="text-sm">四个维度的综合评分</span>
              </label>
            </div>
          </div>

          <div class="flex justify-between">
            <button id="prev-question" class="bg-gray-200 text-gray-700 px-6 py-2 rounded-lg hover:bg-gray-300 transition-colors" disabled>
              <i class="fa fa-arrow-left mr-2"></i> 上一题
            </button>
            <button id="next-question" class="bg-primary text-white px-6 py-2 rounded-lg hover:bg-primary/90 transition-colors">
              下一题 <i class="fa fa-arrow-right ml-2"></i>
            </button>
          </div>
        </div>

        <!-- 评估结果 -->
        <div id="assessment-results" class="hidden">
          <div class="text-center mb-8">
            <div class="w-20 h-20 bg-success/10 rounded-full flex items-center justify-center text-success text-3xl mx-auto mb-4">
              <i class="fa fa-trophy"></i>
            </div>
            <h4 class="text-xl font-bold text-dark mb-4">评估完成！</h4>
            <p class="text-gray-600 mb-6">恭喜您完成销售技能评估，以下是您的详细评估报告：</p>
          </div>

          <!-- 总体评分 -->
          <div class="bg-gradient-to-r from-primary/5 to-secondary/5 rounded-xl p-6 mb-8">
            <div class="text-center">
              <div class="text-4xl font-bold text-primary mb-2" id="overall-score">85</div>
              <div class="text-lg font-medium text-dark mb-2">综合评分</div>
              <div class="text-sm text-gray-600 mb-4">您的销售技能水平：<span class="font-medium text-primary">优秀</span></div>
              <div class="flex justify-center space-x-1">
                <i class="fa fa-star text-warning"></i>
                <i class="fa fa-star text-warning"></i>
                <i class="fa fa-star text-warning"></i>
                <i class="fa fa-star text-warning"></i>
                <i class="fa fa-star-o text-gray-300"></i>
              </div>
            </div>
          </div>

          <!-- 各维度评分 -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
            <div class="space-y-4">
              <h5 class="font-bold text-dark mb-4">技能维度评分</h5>

              <div class="bg-white rounded-lg border p-4">
                <div class="flex justify-between items-center mb-2">
                  <span class="text-sm font-medium">线索获取</span>
                  <span class="text-sm font-bold text-primary">92分</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2">
                  <div class="bg-primary h-2 rounded-full" style="width: 92%"></div>
                </div>
              </div>

              <div class="bg-white rounded-lg border p-4">
                <div class="flex justify-between items-center mb-2">
                  <span class="text-sm font-medium">初步接触</span>
                  <span class="text-sm font-bold text-secondary">88分</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2">
                  <div class="bg-secondary h-2 rounded-full" style="width: 88%"></div>
                </div>
              </div>

              <div class="bg-white rounded-lg border p-4">
                <div class="flex justify-between items-center mb-2">
                  <span class="text-sm font-medium">需求挖掘</span>
                  <span class="text-sm font-bold text-success">85分</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2">
                  <div class="bg-success h-2 rounded-full" style="width: 85%"></div>
                </div>
              </div>

              <div class="bg-white rounded-lg border p-4">
                <div class="flex justify-between items-center mb-2">
                  <span class="text-sm font-medium">邀约演示</span>
                  <span class="text-sm font-bold text-warning">78分</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2">
                  <div class="bg-warning h-2 rounded-full" style="width: 78%"></div>
                </div>
              </div>
            </div>

            <div class="space-y-4">
              <h5 class="font-bold text-dark mb-4">&nbsp;</h5>

              <div class="bg-white rounded-lg border p-4">
                <div class="flex justify-between items-center mb-2">
                  <span class="text-sm font-medium">方案演示</span>
                  <span class="text-sm font-bold text-danger">72分</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2">
                  <div class="bg-danger h-2 rounded-full" style="width: 72%"></div>
                </div>
              </div>

              <div class="bg-white rounded-lg border p-4">
                <div class="flex justify-between items-center mb-2">
                  <span class="text-sm font-medium">异议处理</span>
                  <span class="text-sm font-bold text-info">89分</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2">
                  <div class="bg-info h-2 rounded-full" style="width: 89%"></div>
                </div>
              </div>

              <div class="bg-white rounded-lg border p-4">
                <div class="flex justify-between items-center mb-2">
                  <span class="text-sm font-medium">促成交易</span>
                  <span class="text-sm font-bold text-primary">86分</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2">
                  <div class="bg-primary h-2 rounded-full" style="width: 86%"></div>
                </div>
              </div>

              <div class="bg-white rounded-lg border p-4">
                <div class="flex justify-between items-center mb-2">
                  <span class="text-sm font-medium">客户成功</span>
                  <span class="text-sm font-bold text-success">90分</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2">
                  <div class="bg-success h-2 rounded-full" style="width: 90%"></div>
                </div>
              </div>
            </div>
          </div>

          <!-- 改进建议 -->
          <div class="bg-yellow-50 border border-yellow-200 rounded-xl p-6 mb-8">
            <h5 class="font-bold text-dark mb-4 flex items-center">
              <i class="fa fa-lightbulb-o text-warning mr-2"></i>
              个性化改进建议
            </h5>
            <div class="space-y-3">
              <div class="flex items-start space-x-3">
                <div class="w-6 h-6 bg-warning/20 rounded-full flex items-center justify-center text-warning text-xs font-bold mt-0.5">1</div>
                <div>
                  <h6 class="font-medium text-dark">重点提升：方案演示技巧</h6>
                  <p class="text-sm text-gray-600">建议加强FABE法则的应用，多练习场景化演示，提高客户参与度</p>
                </div>
              </div>
              <div class="flex items-start space-x-3">
                <div class="w-6 h-6 bg-warning/20 rounded-full flex items-center justify-center text-warning text-xs font-bold mt-0.5">2</div>
                <div>
                  <h6 class="font-medium text-dark">优化方向：邀约演示话术</h6>
                  <p class="text-sm text-gray-600">建议学习更多邀约技巧，提高演示邀约的成功率</p>
                </div>
              </div>
              <div class="flex items-start space-x-3">
                <div class="w-6 h-6 bg-success/20 rounded-full flex items-center justify-center text-success text-xs font-bold mt-0.5">3</div>
                <div>
                  <h6 class="font-medium text-dark">保持优势：客户成功管理</h6>
                  <p class="text-sm text-gray-600">您在客户成功方面表现优秀，继续保持并分享经验</p>
                </div>
              </div>
            </div>
          </div>

          <!-- 学习路径推荐 -->
          <div class="bg-blue-50 border border-blue-200 rounded-xl p-6">
            <h5 class="font-bold text-dark mb-4 flex items-center">
              <i class="fa fa-road text-primary mr-2"></i>
              推荐学习路径
            </h5>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div class="bg-white rounded-lg p-4">
                <h6 class="font-medium text-dark mb-2">第一阶段（1-2周）</h6>
                <ul class="text-sm text-gray-600 space-y-1">
                  <li>• 学习FABE法则应用技巧</li>
                  <li>• 练习方案演示话术</li>
                  <li>• 观看演示技巧视频教程</li>
                </ul>
              </div>
              <div class="bg-white rounded-lg p-4">
                <h6 class="font-medium text-dark mb-2">第二阶段（3-4周）</h6>
                <ul class="text-sm text-gray-600 space-y-1">
                  <li>• 参与邀约演示实战训练</li>
                  <li>• 学习客户心理分析</li>
                  <li>• 完成案例分析练习</li>
                </ul>
              </div>
            </div>
          </div>

          <div class="flex justify-center mt-8 space-x-4">
            <button id="download-report" class="bg-primary text-white px-6 py-2 rounded-lg hover:bg-primary/90 transition-colors">
              下载评估报告 <i class="fa fa-download ml-2"></i>
            </button>
            <button id="restart-assessment" class="border border-primary text-primary px-6 py-2 rounded-lg hover:bg-primary/5 transition-colors">
              重新评估 <i class="fa fa-refresh ml-2"></i>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- JavaScript -->
  <script>
    // 初始化所有交互功能
    document.addEventListener('DOMContentLoaded', function() {
      // 阶段标签切换
      initStageTabs();
      // 步骤卡片动画
      initStepCards();
      // 平滑滚动
      initSmoothScroll();
      // 移动端菜单
      initMobileMenu();
      // KPI计算器
      initKPICalculator();
      // 搜索功能
      initSearch();
      // 进度跟踪器
      initProgressTracker();
      // 浮动导航
      initFloatingNav();
      // 返回顶部
      initBackToTop();
      // 话术训练
      initTraining();
      // 数据分析
      initAnalytics();
      // 帮助系统
      initHelpSystem();
      // 全局快捷键
      initGlobalShortcuts();
      // 技能评估
      initAssessment();
      // FAQ交互
      initFAQ();
    });

    // 阶段标签切换功能
    function initStageTabs() {
      const stageTabs = document.querySelectorAll('.stage-tab');
      const stageContents = document.querySelectorAll('.stage-content');

      stageTabs.forEach(tab => {
        tab.addEventListener('click', () => {
          // 移除所有标签的活动状态
          stageTabs.forEach(t => {
            t.classList.remove('active', 'bg-primary', 'text-white');
            t.classList.add('bg-gray-200', 'text-gray-700');
          });

          // 添加当前标签的活动状态
          tab.classList.add('active', 'bg-primary', 'text-white');
          tab.classList.remove('bg-gray-200', 'text-gray-700');

          // 隐藏所有内容
          stageContents.forEach(content => {
            content.classList.add('hidden');
          });

          // 显示对应内容
          const stageId = tab.getAttribute('data-stage');
          const activeContent = document.getElementById(`stage-${stageId}-content`);
          if (activeContent) {
            activeContent.classList.remove('hidden');
          }
        });
      });
    }

    // 平滑滚动功能
    function initSmoothScroll() {
      document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
          e.preventDefault();
          const target = document.querySelector(this.getAttribute('href'));
          if (target) {
            target.scrollIntoView({
              behavior: 'smooth'
            });
            // 关闭移动端菜单
            const mobileMenu = document.getElementById('mobile-menu');
            if (mobileMenu && !mobileMenu.classList.contains('hidden')) {
              mobileMenu.classList.add('hidden');
            }
          }
        });
      });
    }

    // 步骤卡片动画功能
    function initStepCards() {
      const stepCards = document.querySelectorAll('.step-card');

      stepCards.forEach(card => {
        card.addEventListener('mouseenter', () => {
          card.classList.add('shadow-lg', 'scale-105');
        });

        card.addEventListener('mouseleave', () => {
          card.classList.remove('shadow-lg', 'scale-105');
        });
      });
    }

    // 移动端菜单功能
    function initMobileMenu() {
      const mobileMenuBtn = document.getElementById('mobile-menu-btn');
      const mobileMenu = document.getElementById('mobile-menu');

      if (mobileMenuBtn && mobileMenu) {
        mobileMenuBtn.addEventListener('click', () => {
          mobileMenu.classList.toggle('hidden');
        });
      }
    }

    // KPI计算器功能
    function initKPICalculator() {
      const kpiBtn = document.getElementById('kpi-calculator');
      const kpiModal = document.getElementById('kpi-modal');
      const closeBtn = document.getElementById('close-kpi-modal');
      const calculateBtn = document.getElementById('calculate-kpi');

      // 打开模态框
      if (kpiBtn && kpiModal) {
        kpiBtn.addEventListener('click', () => {
          kpiModal.classList.remove('hidden');
        });
      }

      // 关闭模态框
      if (closeBtn && kpiModal) {
        closeBtn.addEventListener('click', () => {
          kpiModal.classList.add('hidden');
        });
      }

      // 点击背景关闭
      if (kpiModal) {
        kpiModal.addEventListener('click', (e) => {
          if (e.target === kpiModal) {
            kpiModal.classList.add('hidden');
          }
        });
      }

      // 计算KPI
      if (calculateBtn) {
        calculateBtn.addEventListener('click', calculateKPI);
      }
    }

    // KPI计算函数
    function calculateKPI() {
      const monthlyLeads = parseFloat(document.getElementById('monthly-leads').value) || 0;
      const callConnectRate = parseFloat(document.getElementById('call-connect-rate').value) || 0;
      const phoneWechatRate = parseFloat(document.getElementById('phone-wechat-rate').value) || 0;
      const wechatDemoRate = parseFloat(document.getElementById('wechat-demo-rate').value) || 0;
      const demoQuoteRate = parseFloat(document.getElementById('demo-quote-rate').value) || 0;
      const quoteCloseRate = parseFloat(document.getElementById('quote-close-rate').value) || 0;
      const avgDealSize = parseFloat(document.getElementById('avg-deal-size').value) || 0;
      const salesTeamSize = parseFloat(document.getElementById('sales-team-size').value) || 1;

      // 计算各阶段数量
      const connectedCalls = monthlyLeads * (callConnectRate / 100);
      const wechatContacts = connectedCalls * (phoneWechatRate / 100);
      const demos = wechatContacts * (wechatDemoRate / 100);
      const quotes = demos * (demoQuoteRate / 100);
      const deals = quotes * (quoteCloseRate / 100);

      // 计算营收
      const monthlyRevenue = deals * avgDealSize;
      const perPersonRevenue = monthlyRevenue / salesTeamSize;

      // 计算整体转化率
      const overallConversion = (deals / monthlyLeads) * 100;

      // 显示结果
      document.getElementById('monthly-deals').textContent = Math.round(deals);
      document.getElementById('monthly-revenue').textContent = '¥' + monthlyRevenue.toLocaleString();
      document.getElementById('per-person-revenue').textContent = '¥' + Math.round(perPersonRevenue).toLocaleString();
      document.getElementById('overall-conversion').textContent = overallConversion.toFixed(2) + '%';

      // 生成优化建议
      generateOptimizationSuggestions({
        callConnectRate,
        phoneWechatRate,
        wechatDemoRate,
        demoQuoteRate,
        quoteCloseRate,
        overallConversion
      });

      // 显示结果区域
      document.getElementById('kpi-results').classList.remove('hidden');
    }

    // 生成优化建议
    function generateOptimizationSuggestions(rates) {
      const suggestions = [];

      if (rates.phoneWechatRate < 60) {
        suggestions.push('• 电话-微信转化率偏低，建议优化开场话术和价值包装');
      }

      if (rates.wechatDemoRate < 30) {
        suggestions.push('• 微信-演示转化率偏低，建议加强需求挖掘和价值传递');
      }

      if (rates.quoteCloseRate < 25) {
        suggestions.push('• 报价-签约转化率偏低，建议强化异议处理和成交技巧');
      }

      if (rates.overallConversion < 4) {
        suggestions.push('• 整体转化率偏低，建议全面优化销售流程和提升团队技能');
      }

      if (suggestions.length === 0) {
        suggestions.push('• 各项指标表现良好，继续保持并寻找进一步优化空间');
      }

      document.getElementById('optimization-suggestions').innerHTML = suggestions.join('<br>');
    }

    // 搜索功能
    function initSearch() {
      const searchBtn = document.getElementById('search-btn');
      const searchModal = document.getElementById('search-modal');
      const closeSearchBtn = document.getElementById('close-search-modal');
      const searchInput = document.getElementById('search-input');
      const searchResults = document.getElementById('search-results');

      // 搜索数据
      const searchData = [
        { title: 'BANT评分模型', content: '预算、权限、需求、时间四个维度评估线索质量', category: '线索获取', stage: 1 },
        { title: 'SPIN销售法', content: '情境、问题、影响、需求四类问题的系统化提问', category: '需求挖掘', stage: 3 },
        { title: 'LAER异议处理', content: '倾听、确认、探索、回应的异议处理框架', category: '异议处理', stage: 6 },
        { title: '开场白话术', content: '您好，我是鑫淼·翼企办的[姓名]...', category: '初步接触', stage: 2 },
        { title: '成交信号识别', content: '强烈、中等、初步三级购买信号的识别方法', category: '促成交易', stage: 7 },
        { title: 'ROI计算器', content: '投资回报率计算工具，量化方案价值', category: '方案演示', stage: 5 },
        { title: '客户成功管理', content: '7天、30天、90天检查点的客户成功路径', category: '客户成功', stage: 8 },
        { title: '价值塑造话术', content: 'FABE法则：功能、优势、利益、证据', category: '邀约演示', stage: 4 }
      ];

      // 打开搜索
      if (searchBtn && searchModal) {
        searchBtn.addEventListener('click', () => {
          searchModal.classList.remove('hidden');
          searchInput.focus();
        });
      }

      // 关闭搜索
      if (closeSearchBtn && searchModal) {
        closeSearchBtn.addEventListener('click', () => {
          searchModal.classList.add('hidden');
          searchInput.value = '';
          searchResults.innerHTML = '<div class="text-gray-500 text-center py-8">输入关键词开始搜索...</div>';
        });
      }

      // 键盘快捷键
      document.addEventListener('keydown', (e) => {
        if (e.ctrlKey && e.key === 'k') {
          e.preventDefault();
          searchModal.classList.remove('hidden');
          searchInput.focus();
        }
        if (e.key === 'Escape' && !searchModal.classList.contains('hidden')) {
          searchModal.classList.add('hidden');
        }
      });

      // 搜索功能
      if (searchInput) {
        searchInput.addEventListener('input', (e) => {
          const query = e.target.value.toLowerCase().trim();

          if (query.length === 0) {
            searchResults.innerHTML = '<div class="text-gray-500 text-center py-8">输入关键词开始搜索...</div>';
            return;
          }

          const results = searchData.filter(item =>
            item.title.toLowerCase().includes(query) ||
            item.content.toLowerCase().includes(query) ||
            item.category.toLowerCase().includes(query)
          );

          if (results.length === 0) {
            searchResults.innerHTML = '<div class="text-gray-500 text-center py-8">未找到相关内容</div>';
            return;
          }

          const resultsHTML = results.map(item => `
            <div class="p-4 border-b border-gray-100 hover:bg-gray-50 cursor-pointer search-result-item" data-stage="${item.stage}">
              <div class="flex items-center justify-between mb-2">
                <h4 class="font-medium text-dark">${item.title}</h4>
                <span class="text-xs bg-primary/10 text-primary px-2 py-1 rounded">${item.category}</span>
              </div>
              <p class="text-sm text-gray-600">${item.content}</p>
            </div>
          `).join('');

          searchResults.innerHTML = resultsHTML;

          // 添加点击事件
          document.querySelectorAll('.search-result-item').forEach(item => {
            item.addEventListener('click', () => {
              const stage = item.getAttribute('data-stage');
              searchModal.classList.add('hidden');

              // 跳转到对应阶段
              const stageTab = document.querySelector(`[data-stage="${stage}"]`);
              if (stageTab) {
                stageTab.click();
                document.getElementById('stages').scrollIntoView({ behavior: 'smooth' });
              }
            });
          });
        });
      }
    }

    // 进度跟踪器功能
    function initProgressTracker() {
      const progressBtn = document.getElementById('progress-tracker-btn');
      const progressModal = document.getElementById('progress-modal');
      const closeProgressBtn = document.getElementById('close-progress-modal');
      const saveProgressBtn = document.getElementById('save-progress');
      const exportProgressBtn = document.getElementById('export-progress');

      // 打开进度跟踪器
      if (progressBtn && progressModal) {
        progressBtn.addEventListener('click', () => {
          progressModal.classList.remove('hidden');
          loadSavedProgress();
        });
      }

      // 关闭进度跟踪器
      if (closeProgressBtn && progressModal) {
        closeProgressBtn.addEventListener('click', () => {
          progressModal.classList.add('hidden');
        });
      }

      // 保存进度
      if (saveProgressBtn) {
        saveProgressBtn.addEventListener('click', saveProgress);
      }

      // 导出报告
      if (exportProgressBtn) {
        exportProgressBtn.addEventListener('click', exportProgress);
      }

      // 阶段复选框事件
      document.querySelectorAll('.progress-stage input[type="checkbox"]').forEach(checkbox => {
        checkbox.addEventListener('change', updateProgressVisual);
      });
    }

    // 保存进度
    function saveProgress() {
      const progressData = {
        customerName: document.getElementById('customer-name').value,
        customerCompany: document.getElementById('customer-company').value,
        customerIndustry: document.getElementById('customer-industry').value,
        stages: {},
        notes: document.getElementById('progress-notes').value,
        nextActions: document.getElementById('next-actions').value,
        lastUpdated: new Date().toISOString()
      };

      // 收集阶段进度
      for (let i = 1; i <= 8; i++) {
        const checkbox = document.getElementById(`stage-${i}`);
        progressData.stages[i] = checkbox.checked;
      }

      // 保存到localStorage
      const customerKey = progressData.customerName || 'default';
      localStorage.setItem(`sales-progress-${customerKey}`, JSON.stringify(progressData));

      // 显示成功消息
      alert('进度已保存！');
    }

    // 加载保存的进度
    function loadSavedProgress() {
      const customerName = document.getElementById('customer-name').value || 'default';
      const savedData = localStorage.getItem(`sales-progress-${customerName}`);

      if (savedData) {
        const progressData = JSON.parse(savedData);

        document.getElementById('customer-name').value = progressData.customerName || '';
        document.getElementById('customer-company').value = progressData.customerCompany || '';
        document.getElementById('customer-industry').value = progressData.customerIndustry || '';
        document.getElementById('progress-notes').value = progressData.notes || '';
        document.getElementById('next-actions').value = progressData.nextActions || '';

        // 恢复阶段进度
        for (let i = 1; i <= 8; i++) {
          const checkbox = document.getElementById(`stage-${i}`);
          checkbox.checked = progressData.stages[i] || false;
        }

        updateProgressVisual();
      }
    }

    // 更新进度可视化
    function updateProgressVisual() {
      const stages = document.querySelectorAll('.progress-stage');
      stages.forEach((stage, index) => {
        const checkbox = stage.querySelector('input[type="checkbox"]');
        if (checkbox.checked) {
          stage.classList.add('bg-success/10', 'border-success');
          stage.classList.remove('bg-gray-50');
        } else {
          stage.classList.remove('bg-success/10', 'border-success');
          stage.classList.add('bg-gray-50');
        }
      });
    }

    // 导出进度报告
    function exportProgress() {
      const progressData = {
        customerName: document.getElementById('customer-name').value,
        customerCompany: document.getElementById('customer-company').value,
        customerIndustry: document.getElementById('customer-industry').value,
        notes: document.getElementById('progress-notes').value,
        nextActions: document.getElementById('next-actions').value
      };

      const stageNames = [
        '线索获取', '初步接触', '需求挖掘', '邀约演示',
        '方案演示', '异议处理', '促成交易', '客户成功'
      ];

      let completedStages = [];
      for (let i = 1; i <= 8; i++) {
        const checkbox = document.getElementById(`stage-${i}`);
        if (checkbox.checked) {
          completedStages.push(stageNames[i-1]);
        }
      }

      const report = `
销售进度报告
================

客户信息：
- 客户名称：${progressData.customerName}
- 公司名称：${progressData.customerCompany}
- 所属行业：${progressData.customerIndustry}

已完成阶段：
${completedStages.map(stage => `- ${stage}`).join('\n')}

进度：${completedStages.length}/8 (${Math.round(completedStages.length/8*100)}%)

备注：
${progressData.notes}

下一步行动：
${progressData.nextActions}

报告生成时间：${new Date().toLocaleString()}
      `;

      // 创建下载链接
      const blob = new Blob([report], { type: 'text/plain' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `销售进度报告-${progressData.customerName || '客户'}-${new Date().toISOString().split('T')[0]}.txt`;
      a.click();
      URL.revokeObjectURL(url);
    }

    // 浮动导航功能
    function initFloatingNav() {
      const navDots = document.querySelectorAll('.nav-dot');
      const sections = ['overview', 'stages', 'resources', 'contact'];

      // 点击导航点
      navDots.forEach((dot, index) => {
        dot.addEventListener('click', () => {
          const target = document.getElementById(sections[index]);
          if (target) {
            target.scrollIntoView({ behavior: 'smooth' });
          }
        });
      });

      // 滚动时更新活动状态
      function updateActiveNav() {
        const scrollY = window.scrollY;
        const windowHeight = window.innerHeight;

        sections.forEach((sectionId, index) => {
          const section = document.getElementById(sectionId);
          if (section) {
            const rect = section.getBoundingClientRect();
            const isActive = rect.top <= windowHeight / 2 && rect.bottom >= windowHeight / 2;

            if (isActive) {
              navDots.forEach(dot => dot.classList.remove('bg-primary'));
              navDots[index].classList.add('bg-primary');
            }
          }
        });
      }

      window.addEventListener('scroll', updateActiveNav);
      updateActiveNav(); // 初始化
    }

    // 返回顶部功能
    function initBackToTop() {
      const backToTopBtn = document.getElementById('back-to-top');

      if (backToTopBtn) {
        // 滚动时显示/隐藏按钮
        window.addEventListener('scroll', () => {
          if (window.scrollY > 300) {
            backToTopBtn.classList.remove('translate-y-16', 'opacity-0');
            backToTopBtn.classList.add('translate-y-0', 'opacity-100');
          } else {
            backToTopBtn.classList.add('translate-y-16', 'opacity-0');
            backToTopBtn.classList.remove('translate-y-0', 'opacity-100');
          }
        });

        // 点击返回顶部
        backToTopBtn.addEventListener('click', () => {
          window.scrollTo({
            top: 0,
            behavior: 'smooth'
          });
        });
      }
    }

    // 话术训练功能
    function initTraining() {
      const trainingBtn = document.getElementById('training-btn');
      const trainingModal = document.getElementById('training-modal');
      const closeTrainingBtn = document.getElementById('close-training-modal');

      // 打开训练模态框
      if (trainingBtn && trainingModal) {
        trainingBtn.addEventListener('click', () => {
          trainingModal.classList.remove('hidden');
          loadTrainingStats();
        });
      }

      // 关闭训练模态框
      if (closeTrainingBtn && trainingModal) {
        closeTrainingBtn.addEventListener('click', () => {
          trainingModal.classList.add('hidden');
        });
      }

      // 训练模式选择
      document.querySelectorAll('.training-mode-btn').forEach(btn => {
        btn.addEventListener('click', () => {
          const mode = btn.getAttribute('data-mode');
          showTrainingMode(mode);

          // 更新按钮状态
          document.querySelectorAll('.training-mode-btn').forEach(b => {
            b.classList.remove('border-primary', 'bg-primary/5');
            b.classList.add('border-gray-200');
          });
          btn.classList.add('border-primary', 'bg-primary/5');
          btn.classList.remove('border-gray-200');
        });
      });

      // 情景训练
      initScenarioTraining();
      // 异议处理训练
      initObjectionTraining();
      // 成交技巧训练
      initClosingTraining();
    }

    // 显示训练模式
    function showTrainingMode(mode) {
      document.getElementById('training-content').classList.remove('hidden');
      document.getElementById('training-stats').classList.remove('hidden');

      // 隐藏所有训练区域
      document.querySelectorAll('.training-section').forEach(section => {
        section.classList.add('hidden');
      });

      // 显示选中的训练区域
      document.getElementById(`${mode}-training`).classList.remove('hidden');
    }

    // 情景训练初始化
    function initScenarioTraining() {
      const startBtn = document.getElementById('start-scenario');
      const dialogueArea = document.getElementById('dialogue-area');
      const sendBtn = document.getElementById('send-response');
      const userInput = document.getElementById('user-response');

      if (startBtn) {
        startBtn.addEventListener('click', startScenarioTraining);
      }

      if (sendBtn) {
        sendBtn.addEventListener('click', sendUserResponse);
      }

      if (userInput) {
        userInput.addEventListener('keypress', (e) => {
          if (e.key === 'Enter') {
            sendUserResponse();
          }
        });
      }

      // 其他按钮
      document.getElementById('get-hint')?.addEventListener('click', showHint);
      document.getElementById('show-best-practice')?.addEventListener('click', showBestPractice);
      document.getElementById('restart-scenario')?.addEventListener('click', restartScenario);
    }

    // 开始情景训练
    function startScenarioTraining() {
      const customerType = document.getElementById('customer-type').value;
      const salesStage = document.getElementById('sales-stage').value;

      document.getElementById('dialogue-area').classList.remove('hidden');

      // 生成初始对话
      const scenarios = getScenarioData(customerType, salesStage);
      const dialogueContent = document.getElementById('dialogue-content');

      dialogueContent.innerHTML = `
        <div class="flex items-start space-x-3 mb-4">
          <div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
            <i class="fa fa-user text-white"></i>
          </div>
          <div class="flex-1 bg-gray-100 rounded-lg p-3">
            <div class="font-medium text-sm text-gray-600 mb-1">客户</div>
            <div>${scenarios.opening}</div>
          </div>
        </div>
      `;

      // 保存当前场景数据
      window.currentScenario = {
        customerType,
        salesStage,
        scenarios,
        step: 0
      };
    }

    // 发送用户回应
    function sendUserResponse() {
      const userInput = document.getElementById('user-response');
      const response = userInput.value.trim();

      if (!response) return;

      const dialogueContent = document.getElementById('dialogue-content');

      // 添加用户回应
      dialogueContent.innerHTML += `
        <div class="flex items-start space-x-3 mb-4 justify-end">
          <div class="flex-1 bg-primary/10 rounded-lg p-3 max-w-md">
            <div class="font-medium text-sm text-primary mb-1">您</div>
            <div>${response}</div>
          </div>
          <div class="w-8 h-8 bg-primary rounded-full flex items-center justify-center">
            <i class="fa fa-user text-white"></i>
          </div>
        </div>
      `;

      // 生成客户回应
      setTimeout(() => {
        const customerResponse = generateCustomerResponse(response);
        dialogueContent.innerHTML += `
          <div class="flex items-start space-x-3 mb-4">
            <div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
              <i class="fa fa-user text-white"></i>
            </div>
            <div class="flex-1 bg-gray-100 rounded-lg p-3">
              <div class="font-medium text-sm text-gray-600 mb-1">客户</div>
              <div>${customerResponse}</div>
            </div>
          </div>
        `;

        dialogueContent.scrollTop = dialogueContent.scrollHeight;
      }, 1000);

      userInput.value = '';
      dialogueContent.scrollTop = dialogueContent.scrollHeight;
    }

    // 获取场景数据
    function getScenarioData(customerType, salesStage) {
      const scenarios = {
        finance: {
          1: { opening: "你好，我们公司对CRM系统有一些了解需求，但不确定是否适合我们的业务模式。" },
          2: { opening: "我们之前用过一些系统，但效果不太理想，你们的系统有什么不同吗？" },
          3: { opening: "我们主要关心的是合规性问题，金融行业监管很严格，你们能保证合规吗？" }
        },
        realestate: {
          1: { opening: "我们是做房地产销售的，团队比较大，客户资源管理确实是个问题。" },
          2: { opening: "我们最担心的是销售离职带走客户，这个问题你们能解决吗？" },
          3: { opening: "我们需要提高销售转化率，现在的成交率不太理想。" }
        },
        education: {
          1: { opening: "我们是教育培训机构，学员管理和销售跟进都比较复杂。" },
          2: { opening: "我们希望能够更好地跟踪学员的学习进度和续费情况。" },
          3: { opening: "我们的销售团队需要更专业的培训和工具支持。" }
        }
      };

      return scenarios[customerType]?.[salesStage] || { opening: "您好，我想了解一下你们的产品。" };
    }

    // 生成客户回应
    function generateCustomerResponse(userResponse) {
      const responses = [
        "这个听起来不错，但是价格方面怎么样？",
        "我需要和团队讨论一下，你们有什么优势吗？",
        "我们之前用过类似的产品，你们有什么不同？",
        "这个功能确实有用，还有其他的特色功能吗？",
        "听起来很专业，但是实施起来会不会很复杂？"
      ];

      return responses[Math.floor(Math.random() * responses.length)];
    }

    // 异议处理训练初始化
    function initObjectionTraining() {
      document.querySelectorAll('.objection-btn').forEach(btn => {
        btn.addEventListener('click', () => {
          const objectionType = btn.getAttribute('data-objection');
          showObjectionResponse(objectionType);

          // 更新按钮状态
          document.querySelectorAll('.objection-btn').forEach(b => {
            b.classList.remove('border-primary', 'bg-primary/5');
          });
          btn.classList.add('border-primary', 'bg-primary/5');
        });
      });
    }

    // 显示异议处理
    function showObjectionResponse(objectionType) {
      const objectionData = {
        price: {
          objection: "你们的价格太贵了，超出了我们的预算。",
          laer: {
            listen: "我理解您对投资回报的关注。",
            acknowledge: "能否请您具体说明，是整体投资规模超出预期，还是某些特定部分的成本问题？",
            explore: "在评估投资回报时，您最关注哪些方面？是前期成本、长期价值，还是回收周期？",
            respond: "让我重新梳理一下投资回报情况：基于您提供的数据，这套解决方案预计每年能为您节省XX万元，投资回收期约为X个月。从长期来看，3年总体拥有成本实际比目前方案低X%。"
          }
        },
        trust: {
          objection: "我需要考虑一下，这个决定比较重要。",
          laer: {
            listen: "完全理解，这确实是一个重要的决定。",
            acknowledge: "能否请您分享一下，主要是哪些方面需要进一步考虑？",
            explore: "在做类似决策时，您通常会评估哪些关键因素？",
            respond: "为了帮助您更好地评估，我可以提供一份详细的ROI分析报告，以及相关行业客户的成功案例。同时，我们也提供试用期，让您可以先体验效果再做决定。"
          }
        }
      };

      const data = objectionData[objectionType];
      if (!data) return;

      const responseArea = document.getElementById('objection-response');
      responseArea.innerHTML = `
        <div class="space-y-4">
          <div class="bg-red-50 border border-red-200 rounded-lg p-4">
            <h6 class="font-medium text-red-800 mb-2">客户异议</h6>
            <p class="text-red-700">"${data.objection}"</p>
          </div>

          <div class="space-y-3">
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-3">
              <h6 class="font-medium text-blue-800 mb-1">L - 倾听</h6>
              <p class="text-blue-700 text-sm">${data.laer.listen}</p>
            </div>

            <div class="bg-green-50 border border-green-200 rounded-lg p-3">
              <h6 class="font-medium text-green-800 mb-1">A - 确认</h6>
              <p class="text-green-700 text-sm">${data.laer.acknowledge}</p>
            </div>

            <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
              <h6 class="font-medium text-yellow-800 mb-1">E - 探索</h6>
              <p class="text-yellow-700 text-sm">${data.laer.explore}</p>
            </div>

            <div class="bg-purple-50 border border-purple-200 rounded-lg p-3">
              <h6 class="font-medium text-purple-800 mb-1">R - 回应</h6>
              <p class="text-purple-700 text-sm">${data.laer.respond}</p>
            </div>
          </div>

          <div class="mt-4">
            <button class="bg-primary text-white px-4 py-2 rounded text-sm hover:bg-primary/90 transition-colors" onclick="practiceObjection('${objectionType}')">
              开始练习 <i class="fa fa-play ml-1"></i>
            </button>
          </div>
        </div>
      `;
    }

    // 成交技巧训练初始化
    function initClosingTraining() {
      document.querySelectorAll('.closing-technique-btn').forEach(btn => {
        btn.addEventListener('click', () => {
          const technique = btn.getAttribute('data-technique');
          showClosingPractice(technique);
        });
      });
    }

    // 显示成交练习
    function showClosingPractice(technique) {
      const practiceArea = document.getElementById('closing-practice');
      const content = document.getElementById('closing-content');

      const techniques = {
        assumptive: {
          title: "假设性成交技巧",
          description: "以假设成交为前提推进后续环节",
          examples: [
            "如果我们今天达成合作，您希望从哪个模块先开始实施？",
            "当我们开始实施时，您希望优先解决哪个业务场景？",
            "我们通常建议客户从销售赋能模块开始，这样能最快看到效果，您觉得呢？"
          ]
        },
        alternative: {
          title: "选择性成交技巧",
          description: "提供两个肯定性选择，而非是否选择",
          examples: [
            "我们可以在本月内启动，也可以安排在下个月初开始实施，哪个时间点更适合您的团队？",
            "您是希望先从5个用户开始试用，还是直接上线20个用户的标准版？",
            "关于付款方式，您是选择年付享受折扣，还是按季度付款更灵活？"
          ]
        },
        urgency: {
          title: "紧迫感成交技巧",
          description: "创造合理的时间压力",
          examples: [
            "目前我们有一个本季度最后的促销活动，可以额外赠送3个月使用期，但需要在本周五前完成签约。",
            "由于项目排期关系，如果能在本季度确认，我们可以保证下月15日前完成部署。",
            "这个优惠政策只针对前10家签约客户，目前还剩3个名额。"
          ]
        }
      };

      const data = techniques[technique];
      if (!data) return;

      content.innerHTML = `
        <h5 class="font-bold text-dark mb-4">${data.title}</h5>
        <p class="text-gray-600 mb-6">${data.description}</p>

        <div class="space-y-4">
          <h6 class="font-medium text-dark">示例话术：</h6>
          ${data.examples.map((example, index) => `
            <div class="bg-gray-50 rounded-lg p-4">
              <div class="flex items-start space-x-3">
                <div class="w-6 h-6 bg-primary rounded-full flex items-center justify-center text-white text-xs font-bold">${index + 1}</div>
                <div class="flex-1">
                  <p class="text-gray-700">"${example}"</p>
                </div>
              </div>
            </div>
          `).join('')}
        </div>

        <div class="mt-6 p-4 bg-blue-50 rounded-lg">
          <h6 class="font-medium text-blue-800 mb-2">练习提示</h6>
          <p class="text-blue-700 text-sm">尝试根据不同的客户情况，调整上述话术的表达方式。记住要自然、真诚，避免过于强硬的推销感。</p>
        </div>
      `;

      practiceArea.classList.remove('hidden');
    }

    // 加载训练统计
    function loadTrainingStats() {
      const stats = JSON.parse(localStorage.getItem('training-stats') || '{}');

      document.getElementById('total-sessions').textContent = stats.totalSessions || 0;
      document.getElementById('success-rate').textContent = (stats.successRate || 0) + '%';
      document.getElementById('best-score').textContent = stats.bestScore || 0;
      document.getElementById('training-time').textContent = stats.trainingTime || 0;
    }

    // 更新训练统计
    function updateTrainingStats(sessionData) {
      const stats = JSON.parse(localStorage.getItem('training-stats') || '{}');

      stats.totalSessions = (stats.totalSessions || 0) + 1;
      stats.trainingTime = (stats.trainingTime || 0) + (sessionData.duration || 0);

      if (sessionData.score > (stats.bestScore || 0)) {
        stats.bestScore = sessionData.score;
      }

      localStorage.setItem('training-stats', JSON.stringify(stats));
      loadTrainingStats();
    }

    // 显示提示
    function showHint() {
      alert('提示：使用SPIN提问法 - 先了解客户情况(S)，再挖掘问题(P)，分析影响(I)，最后建立需求(N)');
    }

    // 显示最佳实践
    function showBestPractice() {
      alert('最佳实践：保持倾听，提出开放性问题，关注客户的情绪变化，适时提供价值证明');
    }

    // 重新开始场景
    function restartScenario() {
      document.getElementById('dialogue-content').innerHTML = '';
      document.getElementById('dialogue-area').classList.add('hidden');
      window.currentScenario = null;
    }

    // 数据分析功能
    function initAnalytics() {
      const analyticsBtn = document.getElementById('analytics-btn');
      const analyticsModal = document.getElementById('analytics-modal');
      const closeAnalyticsBtn = document.getElementById('close-analytics-modal');
      const refreshBtn = document.getElementById('refresh-data');
      const timeRangeSelect = document.getElementById('time-range');

      // 打开数据分析模态框
      if (analyticsBtn && analyticsModal) {
        analyticsBtn.addEventListener('click', () => {
          analyticsModal.classList.remove('hidden');
          loadAnalyticsData();
        });
      }

      // 关闭数据分析模态框
      if (closeAnalyticsBtn && analyticsModal) {
        closeAnalyticsBtn.addEventListener('click', () => {
          analyticsModal.classList.add('hidden');
        });
      }

      // 刷新数据
      if (refreshBtn) {
        refreshBtn.addEventListener('click', () => {
          loadAnalyticsData();
          // 添加刷新动画
          refreshBtn.classList.add('fa-spin');
          setTimeout(() => {
            refreshBtn.classList.remove('fa-spin');
          }, 1000);
        });
      }

      // 时间范围变化
      if (timeRangeSelect) {
        timeRangeSelect.addEventListener('change', loadAnalyticsData);
      }

      // 导出报告
      document.getElementById('export-analytics')?.addEventListener('click', exportAnalyticsReport);

      // 定期报告
      document.getElementById('schedule-report')?.addEventListener('click', scheduleReport);
    }

    // 加载分析数据
    function loadAnalyticsData() {
      const timeRange = document.getElementById('time-range').value;

      // 模拟数据生成（实际应用中应该从API获取）
      const mockData = generateMockAnalyticsData(timeRange);

      // 更新关键指标
      updateKeyMetrics(mockData.metrics);

      // 更新销售漏斗
      updateSalesFunnel(mockData.funnel);

      // 更新转化率
      updateConversionRates(mockData.conversions);

      // 更新团队表现
      updateTeamPerformance(mockData.team);

      // 更新客户来源
      updateCustomerSources(mockData.sources);
    }

    // 生成模拟数据
    function generateMockAnalyticsData(timeRange) {
      const multiplier = timeRange / 30; // 基于30天的倍数

      return {
        metrics: {
          totalLeads: Math.floor(150 * multiplier),
          conversionRate: 4.2 + Math.random() * 2,
          totalRevenue: Math.floor(500000 * multiplier),
          avgCycle: 28 + Math.floor(Math.random() * 10),
          trends: {
            leads: 12 + Math.random() * 8,
            conversion: 5 + Math.random() * 5,
            revenue: 15 + Math.random() * 10,
            cycle: -8 + Math.random() * 5
          }
        },
        funnel: {
          leads: Math.floor(150 * multiplier),
          contact: Math.floor(90 * multiplier),
          needs: Math.floor(60 * multiplier),
          demo: Math.floor(40 * multiplier),
          closed: Math.floor(10 * multiplier)
        },
        conversions: {
          leadContact: 60 + Math.random() * 10,
          contactNeeds: 67 + Math.random() * 8,
          needsDemo: 67 + Math.random() * 8,
          demoClosed: 25 + Math.random() * 10
        },
        team: [
          { name: '张销售', leads: 45, deals: 8, revenue: 120000, conversion: 17.8 },
          { name: '李销售', leads: 38, deals: 6, revenue: 95000, conversion: 15.8 },
          { name: '王销售', leads: 42, deals: 7, revenue: 110000, conversion: 16.7 },
          { name: '刘销售', leads: 35, deals: 5, revenue: 85000, conversion: 14.3 }
        ],
        sources: {
          website: 45 + Math.random() * 10,
          social: 30 + Math.random() * 10,
          referral: 15 + Math.random() * 5,
          other: 10 + Math.random() * 5
        }
      };
    }

    // 更新关键指标
    function updateKeyMetrics(metrics) {
      document.getElementById('total-leads').textContent = metrics.totalLeads;
      document.getElementById('conversion-rate').textContent = metrics.conversionRate.toFixed(1) + '%';
      document.getElementById('total-revenue').textContent = '¥' + metrics.totalRevenue.toLocaleString();
      document.getElementById('avg-cycle').textContent = metrics.avgCycle;

      // 更新趋势
      document.getElementById('leads-trend').textContent = '+' + metrics.trends.leads.toFixed(1) + '%';
      document.getElementById('conversion-trend').textContent = '+' + metrics.trends.conversion.toFixed(1) + '%';
      document.getElementById('revenue-trend').textContent = '+' + metrics.trends.revenue.toFixed(1) + '%';
      document.getElementById('cycle-trend').textContent = metrics.trends.cycle.toFixed(1) + '%';

      // 更新趋势颜色
      updateTrendColor('cycle-trend', metrics.trends.cycle < 0);
    }

    // 更新销售漏斗
    function updateSalesFunnel(funnel) {
      const stages = ['leads', 'contact', 'needs', 'demo', 'closed'];
      const maxValue = funnel.leads;

      stages.forEach(stage => {
        const value = funnel[stage];
        const percentage = (value / maxValue) * 100;

        document.getElementById(`funnel-${stage}`).textContent = value;
        document.getElementById(`funnel-${stage}-bar`).style.width = percentage + '%';
      });
    }

    // 更新转化率
    function updateConversionRates(conversions) {
      document.getElementById('conv-lead-contact').textContent = conversions.leadContact.toFixed(1) + '%';
      document.getElementById('conv-contact-needs').textContent = conversions.contactNeeds.toFixed(1) + '%';
      document.getElementById('conv-needs-demo').textContent = conversions.needsDemo.toFixed(1) + '%';
      document.getElementById('conv-demo-closed').textContent = conversions.demoClosed.toFixed(1) + '%';

      // 计算整体转化率
      const overall = (conversions.leadContact * conversions.contactNeeds * conversions.needsDemo * conversions.demoClosed) / 1000000;
      document.getElementById('overall-conv').textContent = overall.toFixed(2) + '%';
    }

    // 更新团队表现
    function updateTeamPerformance(team) {
      const container = document.getElementById('team-performance');

      container.innerHTML = team.map(member => `
        <div class="flex justify-between items-center p-3 bg-gray-50 rounded">
          <div>
            <div class="font-medium text-dark">${member.name}</div>
            <div class="text-xs text-gray-600">${member.leads}线索 • ${member.deals}成交</div>
          </div>
          <div class="text-right">
            <div class="font-bold text-primary">¥${member.revenue.toLocaleString()}</div>
            <div class="text-xs text-gray-600">${member.conversion.toFixed(1)}%转化率</div>
          </div>
        </div>
      `).join('');
    }

    // 更新客户来源
    function updateCustomerSources(sources) {
      const total = sources.website + sources.social + sources.referral + sources.other;

      const sourceData = [
        { key: 'website', value: sources.website },
        { key: 'social', value: sources.social },
        { key: 'referral', value: sources.referral },
        { key: 'other', value: sources.other }
      ];

      sourceData.forEach(source => {
        const percentage = (source.value / total) * 100;
        document.getElementById(`source-${source.key}`).textContent = percentage.toFixed(0) + '%';
        document.getElementById(`source-${source.key}-bar`).style.width = percentage + '%';
      });
    }

    // 更新趋势颜色
    function updateTrendColor(elementId, isPositive) {
      const element = document.getElementById(elementId);
      if (isPositive) {
        element.classList.remove('text-red-600');
        element.classList.add('text-green-600');
      } else {
        element.classList.remove('text-green-600');
        element.classList.add('text-red-600');
      }
    }

    // 导出分析报告
    function exportAnalyticsReport() {
      const timeRange = document.getElementById('time-range').value;
      const reportData = {
        timeRange: timeRange,
        generatedAt: new Date().toLocaleString(),
        metrics: {
          totalLeads: document.getElementById('total-leads').textContent,
          conversionRate: document.getElementById('conversion-rate').textContent,
          totalRevenue: document.getElementById('total-revenue').textContent,
          avgCycle: document.getElementById('avg-cycle').textContent
        }
      };

      const report = `
销售数据分析报告
==================

报告时间范围: 最近${timeRange}天
生成时间: ${reportData.generatedAt}

关键指标:
- 总线索数: ${reportData.metrics.totalLeads}
- 整体转化率: ${reportData.metrics.conversionRate}
- 总营收: ${reportData.metrics.totalRevenue}
- 平均销售周期: ${reportData.metrics.avgCycle}天

转化率分析:
- 线索→接触: ${document.getElementById('conv-lead-contact').textContent}
- 接触→挖掘: ${document.getElementById('conv-contact-needs').textContent}
- 挖掘→演示: ${document.getElementById('conv-needs-demo').textContent}
- 演示→签约: ${document.getElementById('conv-demo-closed').textContent}

客户来源分析:
- 官网/广告: ${document.getElementById('source-website').textContent}
- 抖音/短视频: ${document.getElementById('source-social').textContent}
- 客户推荐: ${document.getElementById('source-referral').textContent}
- 其他渠道: ${document.getElementById('source-other').textContent}

建议:
1. 继续优化线索质量，提高BANT评分
2. 加强销售团队培训，提升转化率
3. 重点关注高转化率渠道的投入
4. 缩短销售周期，提高效率

报告生成时间: ${new Date().toLocaleString()}
      `;

      // 创建下载链接
      const blob = new Blob([report], { type: 'text/plain' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `销售数据分析报告-${new Date().toISOString().split('T')[0]}.txt`;
      a.click();
      URL.revokeObjectURL(url);
    }

    // 定期报告设置
    function scheduleReport() {
      alert('定期报告功能开发中，将支持每日/每周/每月自动生成报告并发送到指定邮箱。');
    }

    // 帮助系统功能
    function initHelpSystem() {
      const helpBtn = document.getElementById('help-btn');
      const helpModal = document.getElementById('help-modal');
      const closeHelpBtn = document.getElementById('close-help-modal');

      // 打开帮助模态框
      if (helpBtn && helpModal) {
        helpBtn.addEventListener('click', () => {
          helpModal.classList.remove('hidden');
        });
      }

      // 关闭帮助模态框
      if (closeHelpBtn && helpModal) {
        closeHelpBtn.addEventListener('click', () => {
          helpModal.classList.add('hidden');
        });
      }

      // 帮助标签切换
      document.querySelectorAll('.help-tab-btn').forEach(btn => {
        btn.addEventListener('click', () => {
          const tab = btn.getAttribute('data-tab');

          // 更新按钮状态
          document.querySelectorAll('.help-tab-btn').forEach(b => {
            b.classList.remove('bg-primary', 'text-white');
            b.classList.add('bg-gray-200', 'text-gray-700');
          });
          btn.classList.add('bg-primary', 'text-white');
          btn.classList.remove('bg-gray-200', 'text-gray-700');

          // 显示对应内容
          document.querySelectorAll('.help-content').forEach(content => {
            content.classList.add('hidden');
          });
          document.getElementById(`help-${tab}`).classList.remove('hidden');
        });
      });
    }

    // 全局快捷键
    function initGlobalShortcuts() {
      document.addEventListener('keydown', (e) => {
        // Ctrl + K: 打开搜索
        if (e.ctrlKey && e.key === 'k') {
          e.preventDefault();
          const searchModal = document.getElementById('search-modal');
          if (searchModal) {
            searchModal.classList.remove('hidden');
            document.getElementById('search-input').focus();
          }
        }

        // Ctrl + S: 保存进度（如果进度跟踪器打开）
        if (e.ctrlKey && e.key === 's') {
          e.preventDefault();
          const progressModal = document.getElementById('progress-modal');
          if (progressModal && !progressModal.classList.contains('hidden')) {
            saveProgress();
          }
        }

        // F1: 打开帮助
        if (e.key === 'F1') {
          e.preventDefault();
          const helpModal = document.getElementById('help-modal');
          if (helpModal) {
            helpModal.classList.remove('hidden');
          }
        }

        // Home: 返回顶部
        if (e.key === 'Home') {
          e.preventDefault();
          window.scrollTo({ top: 0, behavior: 'smooth' });
        }

        // Escape: 关闭所有模态框
        if (e.key === 'Escape') {
          document.querySelectorAll('[id$="-modal"]').forEach(modal => {
            modal.classList.add('hidden');
          });
        }
      });
    }

    // 页面加载完成后的初始化
    window.addEventListener('load', () => {
      // 显示欢迎提示
      if (!localStorage.getItem('welcome-shown')) {
        setTimeout(() => {
          showWelcomeTooltip();
          localStorage.setItem('welcome-shown', 'true');
        }, 1000);
      }

      // 检查浏览器兼容性
      checkBrowserCompatibility();

      // 初始化性能监控
      initPerformanceMonitoring();
    });

    // 显示欢迎提示
    function showWelcomeTooltip() {
      const tooltip = document.createElement('div');
      tooltip.className = 'fixed top-20 right-6 bg-primary text-white p-4 rounded-lg shadow-lg z-50 max-w-sm';
      tooltip.innerHTML = `
        <div class="flex items-start space-x-3">
          <i class="fa fa-lightbulb-o text-xl mt-1"></i>
          <div>
            <h4 class="font-bold mb-1">欢迎使用销售流程导航图！</h4>
            <p class="text-sm mb-3">按 <kbd class="bg-white/20 px-1 rounded">Ctrl+K</kbd> 快速搜索，按 <kbd class="bg-white/20 px-1 rounded">F1</kbd> 查看帮助。</p>
            <button onclick="this.parentElement.parentElement.parentElement.remove()" class="text-xs underline">知道了</button>
          </div>
        </div>
      `;

      document.body.appendChild(tooltip);

      // 5秒后自动消失
      setTimeout(() => {
        if (tooltip.parentElement) {
          tooltip.remove();
        }
      }, 5000);
    }

    // 检查浏览器兼容性
    function checkBrowserCompatibility() {
      const isCompatible =
        'localStorage' in window &&
        'addEventListener' in window &&
        'querySelector' in document;

      if (!isCompatible) {
        alert('您的浏览器版本过低，可能影响部分功能的使用。建议升级到最新版本的Chrome、Firefox或Safari。');
      }
    }

    // 性能监控
    function initPerformanceMonitoring() {
      // 监控页面加载性能
      if ('performance' in window) {
        window.addEventListener('load', () => {
          setTimeout(() => {
            const perfData = performance.getEntriesByType('navigation')[0];
            const loadTime = perfData.loadEventEnd - perfData.loadEventStart;

            // 如果加载时间超过3秒，显示提示
            if (loadTime > 3000) {
              console.warn('页面加载较慢，建议检查网络连接或清理浏览器缓存');
            }
          }, 0);
        });
      }
    }

    // 错误处理
    window.addEventListener('error', (e) => {
      console.error('页面发生错误:', e.error);

      // 可以在这里添加错误上报逻辑
      // reportError(e.error);
    });

    // 离线检测
    window.addEventListener('online', () => {
      console.log('网络连接已恢复');
    });

    window.addEventListener('offline', () => {
      console.log('网络连接已断开，部分功能可能受影响');
    });

    // 技能评估功能
    function initAssessment() {
      const assessmentBtn = document.getElementById('assessment-btn');
      const assessmentModal = document.getElementById('assessment-modal');
      const closeAssessmentBtn = document.getElementById('close-assessment-modal');
      const startAssessmentBtn = document.getElementById('start-assessment');

      // 打开评估模态框
      if (assessmentBtn && assessmentModal) {
        assessmentBtn.addEventListener('click', () => {
          assessmentModal.classList.remove('hidden');
        });
      }

      // 关闭评估模态框
      if (closeAssessmentBtn && assessmentModal) {
        closeAssessmentBtn.addEventListener('click', () => {
          assessmentModal.classList.add('hidden');
          resetAssessment();
        });
      }

      // 开始评估
      if (startAssessmentBtn) {
        startAssessmentBtn.addEventListener('click', startAssessment);
      }

      // 评估导航
      document.getElementById('next-question')?.addEventListener('click', nextQuestion);
      document.getElementById('prev-question')?.addEventListener('click', prevQuestion);
      document.getElementById('download-report')?.addEventListener('click', downloadAssessmentReport);
      document.getElementById('restart-assessment')?.addEventListener('click', restartAssessment);
    }

    // 评估题库
    const assessmentQuestions = [
      {
        category: "线索获取",
        question: "在进行BANT评分时，以下哪个因素最能准确判断线索的优先级？",
        options: [
          "预算充足程度",
          "决策权限级别",
          "需求紧迫程度",
          "四个维度的综合评分"
        ],
        correct: 3,
        explanation: "BANT评分需要综合考虑预算(Budget)、权限(Authority)、需求(Need)、时间(Timeline)四个维度，单一维度无法准确判断线索优先级。"
      },
      {
        category: "初步接触",
        question: "首次电话联系客户时，最重要的目标是什么？",
        options: [
          "详细介绍产品功能",
          "建立信任并获取微信",
          "了解客户预算情况",
          "安排面对面会议"
        ],
        correct: 1,
        explanation: "初步接触阶段的核心目标是在3分钟内建立信任并获取微信，为后续深度沟通打下基础。"
      },
      {
        category: "需求挖掘",
        question: "SPIN销售法中的'P'代表什么类型的问题？",
        options: [
          "个人问题(Personal)",
          "问题识别(Problem)",
          "产品问题(Product)",
          "价格问题(Price)"
        ],
        correct: 1,
        explanation: "SPIN中的P代表Problem(问题识别)，用于发现客户的痛点和问题。"
      },
      {
        category: "邀约演示",
        question: "邀请客户参加演示时，最有效的方法是？",
        options: [
          "强调产品的先进技术",
          "基于客户痛点设计场景化演示",
          "提供免费试用机会",
          "邀请多个客户一起参加"
        ],
        correct: 1,
        explanation: "场景化演示能够让客户看到解决方案如何具体解决他们的问题，提高演示效果。"
      },
      {
        category: "方案演示",
        question: "方案演示的五步法中，第一步是什么？",
        options: [
          "解决方案呈现",
          "问题确认",
          "价值量化",
          "影响放大"
        ],
        correct: 1,
        explanation: "方案演示五步法：问题确认→影响放大→解决方案呈现→价值量化→证明互动。"
      }
      // 可以继续添加更多题目...
    ];

    let currentAssessment = {
      currentQuestion: 0,
      answers: [],
      startTime: null,
      scores: {
        "线索获取": 0,
        "初步接触": 0,
        "需求挖掘": 0,
        "邀约演示": 0,
        "方案演示": 0,
        "异议处理": 0,
        "促成交易": 0,
        "客户成功": 0
      }
    };

    // 开始评估
    function startAssessment() {
      currentAssessment.startTime = new Date();
      currentAssessment.currentQuestion = 0;
      currentAssessment.answers = [];

      document.getElementById('assessment-intro').classList.add('hidden');
      document.getElementById('assessment-questions').classList.remove('hidden');

      showQuestion(0);
    }

    // 显示问题
    function showQuestion(index) {
      if (index >= assessmentQuestions.length) {
        showResults();
        return;
      }

      const question = assessmentQuestions[index];

      document.getElementById('current-question').textContent = index + 1;
      document.getElementById('total-questions').textContent = assessmentQuestions.length;
      document.getElementById('question-category').textContent = question.category;
      document.getElementById('question-text').textContent = question.question;

      // 更新进度条
      const progress = ((index + 1) / assessmentQuestions.length) * 100;
      document.getElementById('progress-bar').style.width = progress + '%';

      // 生成选项
      const optionsContainer = document.getElementById('question-options');
      optionsContainer.innerHTML = question.options.map((option, i) => `
        <label class="flex items-start space-x-3 p-3 bg-white rounded-lg border hover:border-primary cursor-pointer transition-colors">
          <input type="radio" name="answer" value="${i}" class="mt-1">
          <span class="text-sm">${option}</span>
        </label>
      `).join('');

      // 更新按钮状态
      document.getElementById('prev-question').disabled = index === 0;
      document.getElementById('next-question').textContent =
        index === assessmentQuestions.length - 1 ? '完成评估' : '下一题';
    }

    // 下一题
    function nextQuestion() {
      const selectedAnswer = document.querySelector('input[name="answer"]:checked');
      if (!selectedAnswer) {
        alert('请选择一个答案');
        return;
      }

      // 保存答案
      currentAssessment.answers[currentAssessment.currentQuestion] = parseInt(selectedAnswer.value);

      currentAssessment.currentQuestion++;

      if (currentAssessment.currentQuestion >= assessmentQuestions.length) {
        showResults();
      } else {
        showQuestion(currentAssessment.currentQuestion);
      }
    }

    // 上一题
    function prevQuestion() {
      if (currentAssessment.currentQuestion > 0) {
        currentAssessment.currentQuestion--;
        showQuestion(currentAssessment.currentQuestion);

        // 恢复之前的答案
        const previousAnswer = currentAssessment.answers[currentAssessment.currentQuestion];
        if (previousAnswer !== undefined) {
          const radio = document.querySelector(`input[name="answer"][value="${previousAnswer}"]`);
          if (radio) radio.checked = true;
        }
      }
    }

    // 显示结果
    function showResults() {
      calculateScores();

      document.getElementById('assessment-questions').classList.add('hidden');
      document.getElementById('assessment-results').classList.remove('hidden');

      // 显示总分
      const totalScore = Math.round(Object.values(currentAssessment.scores).reduce((a, b) => a + b, 0) / 8);
      document.getElementById('overall-score').textContent = totalScore;

      // 保存评估结果
      saveAssessmentResult({
        date: new Date().toISOString(),
        score: totalScore,
        scores: currentAssessment.scores,
        duration: Math.round((new Date() - currentAssessment.startTime) / 1000 / 60)
      });
    }

    // 计算分数
    function calculateScores() {
      assessmentQuestions.forEach((question, index) => {
        const userAnswer = currentAssessment.answers[index];
        const isCorrect = userAnswer === question.correct;
        const score = isCorrect ? 100 : Math.max(0, 100 - Math.abs(userAnswer - question.correct) * 25);

        if (!currentAssessment.scores[question.category]) {
          currentAssessment.scores[question.category] = 0;
        }
        currentAssessment.scores[question.category] += score;
      });

      // 计算平均分
      Object.keys(currentAssessment.scores).forEach(category => {
        const questionCount = assessmentQuestions.filter(q => q.category === category).length;
        if (questionCount > 0) {
          currentAssessment.scores[category] = Math.round(currentAssessment.scores[category] / questionCount);
        }
      });
    }

    // 保存评估结果
    function saveAssessmentResult(result) {
      const results = JSON.parse(localStorage.getItem('assessment-results') || '[]');
      results.push(result);
      localStorage.setItem('assessment-results', JSON.stringify(results));
    }

    // 下载评估报告
    function downloadAssessmentReport() {
      const totalScore = document.getElementById('overall-score').textContent;
      const report = `
销售技能评估报告
==================

评估时间: ${new Date().toLocaleString()}
综合评分: ${totalScore}分

各维度评分:
${Object.entries(currentAssessment.scores).map(([category, score]) =>
  `- ${category}: ${score}分`
).join('\n')}

评估建议:
1. 重点提升得分较低的技能维度
2. 参与相关的话术训练和实战练习
3. 定期重新评估，跟踪进步情况
4. 与团队分享优秀的销售经验

报告生成时间: ${new Date().toLocaleString()}
      `;

      const blob = new Blob([report], { type: 'text/plain' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `销售技能评估报告-${new Date().toISOString().split('T')[0]}.txt`;
      a.click();
      URL.revokeObjectURL(url);
    }

    // 重新评估
    function restartAssessment() {
      resetAssessment();
      startAssessment();
    }

    // 重置评估
    function resetAssessment() {
      currentAssessment = {
        currentQuestion: 0,
        answers: [],
        startTime: null,
        scores: {
          "线索获取": 0,
          "初步接触": 0,
          "需求挖掘": 0,
          "邀约演示": 0,
          "方案演示": 0,
          "异议处理": 0,
          "促成交易": 0,
          "客户成功": 0
        }
      };

      document.getElementById('assessment-intro').classList.remove('hidden');
      document.getElementById('assessment-questions').classList.add('hidden');
      document.getElementById('assessment-results').classList.add('hidden');
    }

    // FAQ交互功能
    function initFAQ() {
      document.querySelectorAll('.faq-question').forEach(question => {
        question.addEventListener('click', () => {
          const targetId = question.getAttribute('data-target');
          const answer = document.getElementById(targetId);
          const icon = question.querySelector('i');

          // 切换显示状态
          if (answer.classList.contains('hidden')) {
            // 关闭其他FAQ
            document.querySelectorAll('.faq-answer').forEach(otherAnswer => {
              otherAnswer.classList.add('hidden');
            });
            document.querySelectorAll('.faq-question i').forEach(otherIcon => {
              otherIcon.classList.remove('fa-chevron-up');
              otherIcon.classList.add('fa-chevron-down');
            });

            // 打开当前FAQ
            answer.classList.remove('hidden');
            icon.classList.remove('fa-chevron-down');
            icon.classList.add('fa-chevron-up');
          } else {
            // 关闭当前FAQ
            answer.classList.add('hidden');
            icon.classList.remove('fa-chevron-up');
            icon.classList.add('fa-chevron-down');
          }
        });
      });
    }

    // 添加更多实用功能

    // 销售数据导出功能
    function exportSalesData() {
      const data = {
        exportTime: new Date().toLocaleString(),
        progressData: JSON.parse(localStorage.getItem('progress-data') || '{}'),
        trainingStats: JSON.parse(localStorage.getItem('training-stats') || '{}'),
        assessmentResults: JSON.parse(localStorage.getItem('assessment-results') || '[]')
      };

      const jsonData = JSON.stringify(data, null, 2);
      const blob = new Blob([jsonData], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `销售数据导出-${new Date().toISOString().split('T')[0]}.json`;
      a.click();
      URL.revokeObjectURL(url);
    }

    // 销售数据导入功能
    function importSalesData() {
      const input = document.createElement('input');
      input.type = 'file';
      input.accept = '.json';
      input.onchange = (e) => {
        const file = e.target.files[0];
        if (file) {
          const reader = new FileReader();
          reader.onload = (e) => {
            try {
              const data = JSON.parse(e.target.result);

              if (data.progressData) {
                localStorage.setItem('progress-data', JSON.stringify(data.progressData));
              }
              if (data.trainingStats) {
                localStorage.setItem('training-stats', JSON.stringify(data.trainingStats));
              }
              if (data.assessmentResults) {
                localStorage.setItem('assessment-results', JSON.stringify(data.assessmentResults));
              }

              alert('数据导入成功！');
              location.reload();
            } catch (error) {
              alert('数据格式错误，导入失败！');
            }
          };
          reader.readAsText(file);
        }
      };
      input.click();
    }

    // 添加数据管理按钮到页面
    function addDataManagementButtons() {
      const footer = document.querySelector('footer .container');
      if (footer) {
        const dataManagement = document.createElement('div');
        dataManagement.className = 'mt-8 pt-8 border-t border-gray-600 text-center';
        dataManagement.innerHTML = `
          <h4 class="font-bold mb-4 text-white">数据管理</h4>
          <div class="flex justify-center space-x-4">
            <button onclick="exportSalesData()" class="bg-primary text-white px-4 py-2 rounded text-sm hover:bg-primary/90 transition-colors">
              <i class="fa fa-download mr-2"></i>导出数据
            </button>
            <button onclick="importSalesData()" class="bg-secondary text-white px-4 py-2 rounded text-sm hover:bg-secondary/90 transition-colors">
              <i class="fa fa-upload mr-2"></i>导入数据
            </button>
          </div>
        `;
        footer.appendChild(dataManagement);
      }
    }

    // 页面完全加载后执行
    window.addEventListener('load', () => {
      addDataManagementButtons();

      // 检查是否有保存的数据
      const hasData = localStorage.getItem('progress-data') ||
                     localStorage.getItem('training-stats') ||
                     localStorage.getItem('assessment-results');

      if (hasData) {
        console.log('检测到本地保存的销售数据');
      }
    });
  </script>
</body>
</html>
    