<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>鑫淼·翼企办销售流程导航图</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <link href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css" rel="stylesheet">
  
  <!-- 配置Tailwind -->
  <script>
    tailwind.config = {
      theme: {
        extend: {
          colors: {
            primary: '#165DFF',
            secondary: '#0FC6C2',
            success: '#00B42A',
            warning: '#FF7D00',
            danger: '#F53F3F',
            info: '#86909C',
            light: '#F2F3F5',
            dark: '#1D2129',
          },
          fontFamily: {
            inter: ['Inter', 'system-ui', 'sans-serif'],
          },
          boxShadow: {
            'card': '0 4px 20px rgba(0, 0, 0, 0.08)',
            'card-hover': '0 8px 30px rgba(0, 0, 0, 0.12)',
          }
        },
      }
    }
  </script>
  
  <style type="text/tailwindcss">
    @layer utilities {
      .content-auto {
        content-visibility: auto;
      }
      .text-shadow {
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }
      .transition-transform-opacity {
        transition-property: transform, opacity;
        transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
        transition-duration: 300ms;
      }
      .scale-hover {
        @apply hover:scale-105 transition-all duration-300;
      }
      .stage-card {
        @apply bg-white rounded-xl shadow-card p-6 scale-hover;
      }
      .process-line {
        @apply h-1 bg-primary/30 relative;
      }
      .process-line::after {
        content: '';
        @apply absolute top-0 left-0 h-full bg-primary transition-all duration-1000 ease-out;
      }
      .process-line-complete::after {
        @apply w-full;
      }
      .process-line-half::after {
        @apply w-1/2;
      }
      .step-active {
        @apply border-primary bg-primary/5;
      }
      .step-completed {
        @apply border-success bg-success/5;
      }
      .step-icon {
        @apply w-10 h-10 rounded-full flex items-center justify-center text-lg font-bold transition-all duration-300;
      }
      .step-active .step-icon {
        @apply bg-primary text-white border-primary;
      }
      .step-completed .step-icon {
        @apply bg-success text-white border-success;
      }
      .step-inactive .step-icon {
        @apply bg-gray-100 text-gray-400 border-gray-200;
      }
    }
  </style>
</head>
<body class="bg-gray-50 font-inter text-dark">
  <!-- 顶部导航 -->
  <header class="bg-white shadow-sm sticky top-0 z-50">
    <div class="container mx-auto px-4 py-4 flex items-center justify-between">
      <div class="flex items-center space-x-3">
        <div class="text-primary text-2xl">
          <i class="fa fa-line-chart"></i>
        </div>
        <h1 class="text-xl font-bold text-dark">鑫淼·翼企办<span class="text-primary">销售流程导航图</span></h1>
      </div>
      <nav class="hidden md:flex space-x-8">
        <a href="#overview" class="text-dark hover:text-primary transition-colors">流程概览</a>
        <a href="#stages" class="text-dark hover:text-primary transition-colors">详细阶段</a>
        <a href="#resources" class="text-dark hover:text-primary transition-colors">销售资源</a>
        <a href="#contact" class="text-dark hover:text-primary transition-colors">联系我们</a>
      </nav>
      <button class="md:hidden text-dark text-xl">
        <i class="fa fa-bars"></i>
      </button>
    </div>
  </header>

  <!-- 英雄区域 -->
  <section class="bg-gradient-to-r from-primary/90 to-primary py-16 md:py-24">
    <div class="container mx-auto px-4">
      <div class="max-w-4xl mx-auto text-center">
        <h2 class="text-[clamp(2rem,5vw,3.5rem)] font-bold text-white mb-6 leading-tight text-shadow">
          高效销售流程，<br class="hidden md:block">卓越客户转化
        </h2>
        <p class="text-[clamp(1rem,2vw,1.25rem)] text-white/90 mb-8 max-w-3xl mx-auto">
          清晰的销售流程是成功的关键。我们精心设计的导航图将帮助您的团队有效跟进和转化客户，从线索获取到客户成功的完整旅程。
        </p>
        <div class="flex flex-col sm:flex-row justify-center gap-4">
          <a href="#stages" class="bg-white text-primary px-8 py-3 rounded-lg font-medium hover:bg-gray-100 transition-colors shadow-lg hover:shadow-xl transform hover:-translate-y-1 duration-300">
            探索流程 <i class="fa fa-arrow-right ml-2"></i>
          </a>
          <a href="#resources" class="bg-white/20 text-white border border-white/30 px-8 py-3 rounded-lg font-medium hover:bg-white/30 transition-colors">
            查看资源 <i class="fa fa-book ml-2"></i>
          </a>
        </div>
      </div>
    </div>
  </section>

  <!-- 流程概览 -->
  <section id="overview" class="py-16 bg-white">
    <div class="container mx-auto px-4">
      <div class="text-center mb-12">
        <h2 class="text-[clamp(1.5rem,3vw,2.5rem)] font-bold text-dark mb-4">销售流程概览</h2>
        <p class="text-gray-600 max-w-2xl mx-auto">我们的销售流程分为八个关键阶段，每个阶段都有明确的目标和行动点，确保销售团队能够高效地推进客户转化。</p>
      </div>
      
      <!-- 流程步骤时间线 -->
      <div class="relative">
        <!-- 连接线 -->
        <div class="hidden md:block absolute left-1/2 top-8 -translate-x-1/2 w-1 h-full bg-gray-200 z-0"></div>
        
        <!-- 步骤卡片 -->
        <div class="space-y-12 md:space-y-24 relative z-10">
          <!-- 步骤1 -->
          <div class="flex flex-col md:flex-row items-center">
            <div class="md:w-1/2 md:pr-12 mb-6 md:mb-0 md:text-right">
              <div class="stage-card">
                <h3 class="text-xl font-bold text-primary mb-3">线索获取阶段</h3>
                <p class="text-gray-600">通过各种渠道获取潜在客户线索，并进行评分分类，确定跟进优先级。</p>
              </div>
            </div>
            <div class="flex items-center justify-center mb-6 md:mb-0">
              <div class="step-active rounded-full border-2 w-16 h-16 flex items-center justify-center text-primary text-2xl font-bold shadow-lg">
                1
              </div>
            </div>
            <div class="md:w-1/2 md:pl-12">
              <div class="bg-gray-50 rounded-xl p-6 shadow-sm">
                <h4 class="font-bold text-dark mb-2">关键行动点</h4>
                <ul class="text-gray-600 space-y-2">
                  <li class="flex items-start">
                    <i class="fa fa-check-circle text-success mt-1 mr-2"></i>
                    <span>对所有线索进行BANT评分(预算、权限、需求、时间)</span>
                  </li>
                  <li class="flex items-start">
                    <i class="fa fa-check-circle text-success mt-1 mr-2"></i>
                    <span>根据分数将线索分为A/B/C三级</span>
                  </li>
                  <li class="flex items-start">
                    <i class="fa fa-check-circle text-success mt-1 mr-2"></i>
                    <span>A级线索24小时内联系，B级48小时内，C级纳入自动化培育</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>
          
          <!-- 步骤2 -->
          <div class="flex flex-col md:flex-row items-center">
            <div class="md:w-1/2 md:pr-12 mb-6 md:mb-0 md:text-right order-1 md:order-2">
              <div class="stage-card">
                <h3 class="text-xl font-bold text-primary mb-3">初步接触阶段</h3>
                <p class="text-gray-600">通过电话或其他渠道首次接触潜在客户，建立信任，获取联系方式，为后续跟进打下基础。</p>
              </div>
            </div>
            <div class="flex items-center justify-center mb-6 md:mb-0 order-2 md:order-1">
              <div class="step-inactive rounded-full border-2 w-16 h-16 flex items-center justify-center text-gray-400 text-2xl font-bold shadow-lg">
                2
              </div>
            </div>
            <div class="md:w-1/2 md:pl-12 order-3">
              <div class="bg-gray-50 rounded-xl p-6 shadow-sm">
                <h4 class="font-bold text-dark mb-2">关键行动点</h4>
                <ul class="text-gray-600 space-y-2">
                  <li class="flex items-start">
                    <i class="fa fa-check-circle text-success mt-1 mr-2"></i>
                    <span>准备30秒电梯pitch，快速引起兴趣</span>
                  </li>
                  <li class="flex items-start">
                    <i class="fa fa-check-circle text-success mt-1 mr-2"></i>
                    <span>首次联系目标：获取微信，不强推产品</span>
                  </li>
                  <li class="flex items-start">
                    <i class="fa fa-check-circle text-success mt-1 mr-2"></i>
                    <span>未接通策略：电话+短信+邮件多渠道尝试</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>
          
          <!-- 步骤3 -->
          <div class="flex flex-col md:flex-row items-center">
            <div class="md:w-1/2 md:pr-12 mb-6 md:mb-0 md:text-right">
              <div class="stage-card">
                <h3 class="text-xl font-bold text-primary mb-3">需求挖掘阶段</h3>
                <p class="text-gray-600">通过深入沟通，了解客户的具体需求和痛点，为客户提供针对性的解决方案。</p>
              </div>
            </div>
            <div class="flex items-center justify-center mb-6 md:mb-0">
              <div class="step-inactive rounded-full border-2 w-16 h-16 flex items-center justify-center text-gray-400 text-2xl font-bold shadow-lg">
                3
              </div>
            </div>
            <div class="md:w-1/2 md:pl-12">
              <div class="bg-gray-50 rounded-xl p-6 shadow-sm">
                <h4 class="font-bold text-dark mb-2">关键行动点</h4>
                <ul class="text-gray-600 space-y-2">
                  <li class="flex items-start">
                    <i class="fa fa-check-circle text-success mt-1 mr-2"></i>
                    <span>使用5W2H方法论挖掘需求(What/Why/Who/When/Where/How/How much)</span>
                  </li>
                  <li class="flex items-start">
                    <i class="fa fa-check-circle text-success mt-1 mr-2"></i>
                    <span>关注客户情绪变化，识别兴趣点</span>
                  </li>
                  <li class="flex items-start">
                    <i class="fa fa-check-circle text-success mt-1 mr-2"></i>
                    <span>收集具体业务数据，为后续方案做准备</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>
          
          <!-- 步骤4-8 简略展示 -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-12">
            <div class="bg-white rounded-xl shadow-card p-5 hover:shadow-card-hover transition-shadow">
              <div class="flex items-center mb-4">
                <div class="step-inactive rounded-full border-2 w-10 h-10 flex items-center justify-center text-gray-400 font-bold">
                  4
                </div>
                <h3 class="font-bold text-dark ml-4">邀约演示阶段</h3>
              </div>
              <p class="text-gray-600 text-sm">邀请客户进行产品或服务演示，展示解决方案的价值和优势。</p>
            </div>
            <div class="bg-white rounded-xl shadow-card p-5 hover:shadow-card-hover transition-shadow">
              <div class="flex items-center mb-4">
                <div class="step-inactive rounded-full border-2 w-10 h-10 flex items-center justify-center text-gray-400 font-bold">
                  5
                </div>
                <h3 class="font-bold text-dark ml-4">方案演示阶段</h3>
              </div>
              <p class="text-gray-600 text-sm">向客户详细演示定制化解决方案，解答疑问，获取反馈。</p>
            </div>
            <div class="bg-white rounded-xl shadow-card p-5 hover:shadow-card-hover transition-shadow">
              <div class="flex items-center mb-4">
                <div class="step-inactive rounded-full border-2 w-10 h-10 flex items-center justify-center text-gray-400 font-bold">
                  6
                </div>
                <h3 class="font-bold text-dark ml-4">异议处理阶段</h3>
              </div>
              <p class="text-gray-600 text-sm">识别并处理客户提出的异议和顾虑，增强客户信心。</p>
            </div>
            <div class="bg-white rounded-xl shadow-card p-5 hover:shadow-card-hover transition-shadow">
              <div class="flex items-center mb-4">
                <div class="step-inactive rounded-full border-2 w-10 h-10 flex items-center justify-center text-gray-400 font-bold">
                  7
                </div>
                <h3 class="font-bold text-dark ml-4">促成交易阶段</h3>
              </div>
              <p class="text-gray-600 text-sm">识别购买信号，促进客户做出购买决策，完成交易。</p>
            </div>
            <div class="bg-white rounded-xl shadow-card p-5 hover:shadow-card-hover transition-shadow">
              <div class="flex items-center mb-4">
                <div class="step-inactive rounded-full border-2 w-10 h-10 flex items-center justify-center text-gray-400 font-bold">
                  8
                </div>
                <h3 class="font-bold text-dark ml-4">客户成功阶段</h3>
              </div>
              <p class="text-gray-600 text-sm">确保客户成功实施解决方案，提供持续支持，促进客户满意度和忠诚度。</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- 详细阶段 -->
  <section id="stages" class="py-16 bg-gray-50">
    <div class="container mx-auto px-4">
      <div class="text-center mb-12">
        <h2 class="text-[clamp(1.5rem,3vw,2.5rem)] font-bold text-dark mb-4">详细阶段导航</h2>
        <p class="text-gray-600 max-w-2xl mx-auto">探索销售流程的每个阶段，了解关键步骤、决策点和行动指南。</p>
      </div>
      
      <!-- 阶段选择标签 -->
      <div class="flex flex-wrap justify-center gap-3 mb-10">
        <button class="stage-tab active px-6 py-3 rounded-full bg-primary text-white font-medium transition-all" data-stage="1">
          线索获取阶段
        </button>
        <button class="stage-tab px-6 py-3 rounded-full bg-gray-200 text-gray-700 font-medium transition-all hover:bg-gray-300" data-stage="2">
          初步接触阶段
        </button>
        <button class="stage-tab px-6 py-3 rounded-full bg-gray-200 text-gray-700 font-medium transition-all hover:bg-gray-300" data-stage="3">
          需求挖掘阶段
        </button>
        <button class="stage-tab px-6 py-3 rounded-full bg-gray-200 text-gray-700 font-medium transition-all hover:bg-gray-300" data-stage="4">
          邀约演示阶段
        </button>
        <button class="stage-tab px-6 py-3 rounded-full bg-gray-200 text-gray-700 font-medium transition-all hover:bg-gray-300" data-stage="5">
          方案演示阶段
        </button>
        <button class="stage-tab px-6 py-3 rounded-full bg-gray-200 text-gray-700 font-medium transition-all hover:bg-gray-300" data-stage="6">
          异议处理阶段
        </button>
        <button class="stage-tab px-6 py-3 rounded-full bg-gray-200 text-gray-700 font-medium transition-all hover:bg-gray-300" data-stage="7">
          促成交易阶段
        </button>
        <button class="stage-tab px-6 py-3 rounded-full bg-gray-200 text-gray-700 font-medium transition-all hover:bg-gray-300" data-stage="8">
          客户成功阶段
        </button>
      </div>
      
      <!-- 阶段内容 -->
      <div class="stage-content" id="stage-1-content">
        <div class="bg-white rounded-xl shadow-card p-6 md:p-8 mb-8">
          <div class="flex flex-col md:flex-row gap-8">
            <div class="md:w-1/3">
              <div class="bg-primary/5 rounded-lg p-6">
                                <h3 class="text-xl font-bold text-primary mb-4 flex items-center">
                  <i class="fa fa-lightbulb-o mr-3"></i> 核心理念
                </h3>
                <p class="text-gray-600 mb-4 font-medium italic">停止推销，开始引导。我们不是在卖产品，而是在为客户诊断问题并提供解决方案。销售是帮助客户成功的自然结果。</p>
                
                <div class="mt-6">
                  <h4 class="font-bold text-dark mb-4">阶段目标</h4>
                  <div class="bg-primary/5 rounded-lg p-4 mb-4">
                    <p class="text-primary font-bold">3分钟内建立信任，拿到微信</p>
                  </div>
                  
                  <h4 class="font-bold text-dark mb-2 mt-6">线索类型分析</h4>
                  <div class="space-y-4">
                    <div class="border-l-4 border-primary pl-4 py-2">
                      <h5 class="font-bold text-dark mb-2">官网/广告线索 (被动型)</h5>
                      <ul class="text-gray-600 space-y-2">
                        <li class="flex items-start">
                          <i class="fa fa-user text-primary mt-1 mr-2"></i>
                          <span>用户画像：主动搜索，有明确需求或痛点，认知度较高</span>
                        </li>
                        <li class="flex items-start">
                          <i class="fa fa-comments text-primary mt-1 mr-2"></i>
                          <span>沟通策略：必须体现专业性，快速切入核心问题，提供价值</span>
                        </li>
                        <li class="flex items-start">
                          <i class="fa fa-id-card text-primary mt-1 mr-2"></i>
                          <span>你的角色：专家顾问</span>
                        </li>
                      </ul>
                    </div>
                    
                    <div class="border-l-4 border-secondary pl-4 py-2">
                      <h5 class="font-bold text-dark mb-2">抖音线索 (主动型)</h5>
                      <ul class="text-gray-600 space-y-2">
                        <li class="flex items-start">
                          <i class="fa fa-user text-secondary mt-1 mr-2"></i>
                          <span>用户画像：被内容吸引，可能处于问题萌芽期，认知度浅</span>
                        </li>
                        <li class="flex items-start">
                          <i class="fa fa-comments text-secondary mt-1 mr-2"></i>
                          <span>沟通策略：需要先建立兴趣和信任，用通俗易懂的方式"翻译"产品价值，过程要更有趣、更轻松</span>
                        </li>
                        <li class="flex items-start">
                          <i class="fa fa-id-card text-secondary mt-1 mr-2"></i>
                          <span>你的角色：懂技术的朋友</span>
                        </li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="md:w-2/3">
              <h3 class="text-xl font-bold text-dark mb-6 flex items-center">
                <i class="fa fa-sitemap mr-3"></i> 流程步骤
              </h3>
              
              <!-- 流程图 -->
              <div class="bg-gray-50 rounded-lg p-6">
                <div class="flex flex-col space-y-8">
                  <!-- 步骤1 -->
                  <div class="step-card step-active rounded-lg border p-4">
                    <div class="flex items-center">
                      <div class="step-icon border-2">1</div>
                      <h4 class="font-bold text-dark ml-4">线索进入</h4>
                    </div>
                    <p class="text-gray-600 mt-3">通过市场活动、广告、社交媒体、网站表单、合作伙伴推荐等渠道获取潜在客户信息。</p>
                  </div>
                  
                  <!-- 连接线 -->
                  <div class="process-line mx-8">
                    <div class="absolute top-1/2 left-0 w-4 h-4 bg-primary rounded-full -translate-y-1/2 -translate-x-1/2"></div>
                    <div class="absolute top-1/2 right-0 w-4 h-4 bg-primary rounded-full -translate-y-1/2 translate-x-1/2"></div>
                  </div>
                  
                  <!-- 步骤2 -->
                  <div class="step-card step-active rounded-lg border p-4">
                    <div class="flex items-center">
                      <div class="step-icon border-2">2</div>
                      <h4 class="font-bold text-dark ml-4">线索评分</h4>
                    </div>
                    <p class="text-gray-600 mt-3">使用BANT方法(预算、权限、需求、时间)对线索进行评分，确定线索质量和优先级。</p>
                  </div>
                  
                  <!-- 决策点 -->
                  <div class="mx-8 flex flex-col items-center">
                    <div class="w-6 h-6 bg-primary rounded-full flex items-center justify-center text-white text-xs">
                      ?
                    </div>
                    <p class="text-center text-sm font-medium mt-2">是否为高质量线索?</p>
                  </div>
                  
                  <!-- 分支 -->
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- 分支1 -->
                    <div class="step-card step-active rounded-lg border p-4">
                      <div class="flex items-center">
                        <div class="step-icon border-2 bg-success text-white">是</div>
                      </div>
                      <div class="mt-3">
                        <h4 class="font-bold text-dark">优先分配给资深销售</h4>
                        <p class="text-gray-600 mt-2">将高质量线索优先分配给经验丰富的销售代表，确保能够快速建立联系并有效跟进。</p>
                        
                        <div class="mt-4 step-card step-active rounded-lg border p-3">
                          <h4 class="font-bold text-dark flex items-center">
                            <i class="fa fa-arrow-right text-primary mr-2"></i> 24小时内联系
                          </h4>
                          <p class="text-gray-600 mt-2">资深销售在24小时内主动联系潜在客户，进行初步沟通和需求了解。</p>
                        </div>
                      </div>
                    </div>
                    
                    <!-- 分支2 -->
                    <div class="step-card step-inactive rounded-lg border p-4">
                      <div class="flex items-center">
                        <div class="step-icon border-2 bg-warning text-white">否</div>
                      </div>
                      <div class="mt-3">
                        <h4 class="font-bold text-dark">进入培育序列</h4>
                        <p class="text-gray-600 mt-2">对于质量较低的线索，将其纳入自动化培育序列，通过定期内容推送保持联系。</p>
                        
                        <div class="mt-4 step-card step-inactive rounded-lg border p-3">
                          <h4 class="font-bold text-dark flex items-center">
                            <i class="fa fa-arrow-right text-primary mr-2"></i> 定期内容推送
                          </h4>
                          <p class="text-gray-600 mt-2">向潜在客户发送有价值的内容，如行业报告、解决方案介绍、成功案例等，建立信任和兴趣。</p>
                        </div>
                        
                        <div class="mt-4 step-card step-inactive rounded-lg border p-3">
                          <h4 class="font-bold text-dark flex items-center">
                            <i class="fa fa-arrow-right text-primary mr-2"></i> 7天后重新评估
                          </h4>
                          <p class="text-gray-600 mt-2">在7天后重新评估线索质量，看是否有足够的兴趣和需求升级为高质量线索。</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 关键行动点 -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <div class="bg-white rounded-xl shadow-card p-6 hover:shadow-card-hover transition-shadow">
            <div class="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center text-primary text-xl mb-4">
              <i class="fa fa-star"></i>
            </div>
            <h3 class="font-bold text-dark mb-3">BANT评分</h3>
            <p class="text-gray-600">对所有线索进行BANT评分(预算、权限、需求、时间)，确保对线索质量有清晰的认识。</p>
          </div>
          
          <div class="bg-white rounded-xl shadow-card p-6 hover:shadow-card-hover transition-shadow">
            <div class="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center text-primary text-xl mb-4">
              <i class="fa fa-tags"></i>
            </div>
            <h3 class="font-bold text-dark mb-3">线索分级</h3>
            <p class="text-gray-600">根据BANT评分结果，将线索分为A/B/C三级，A级为最高优先级，C级为最低优先级。</p>
          </div>
          
          <div class="bg-white rounded-xl shadow-card p-6 hover:shadow-card-hover transition-shadow">
            <div class="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center text-primary text-xl mb-4">
              <i class="fa fa-clock-o"></i>
            </div>
            <h3 class="font-bold text-dark mb-3">响应时间</h3>
            <p class="text-gray-600">根据线索级别确定响应时间，A级线索24小时内联系，B级48小时内，C级纳入自动化培育。</p>
          </div>
        </div>
        
        <!-- 工具和模板 -->
        <div class="bg-white rounded-xl shadow-card p-6">
          <h3 class="text-xl font-bold text-dark mb-6">工具和模板</h3>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="border rounded-lg p-4 flex items-start hover:bg-gray-50 transition-colors">
              <div class="text-primary text-xl mt-1 mr-4">
                <i class="fa fa-file-text-o"></i>
              </div>
              <div>
                <h4 class="font-bold text-dark">线索评分表</h4>
                <p class="text-gray-600 text-sm mt-1">用于评估线索质量的BANT评分表模板，帮助销售团队标准化线索评估流程。</p>
                <a href="#" class="text-primary text-sm mt-2 inline-block hover:underline">
                  下载模板 <i class="fa fa-download ml-1"></i>
                </a>
              </div>
            </div>
            
            <div class="border rounded-lg p-4 flex items-start hover:bg-gray-50 transition-colors">
              <div class="text-primary text-xl mt-1 mr-4">
                <i class="fa fa-file-text-o"></i>
              </div>
              <div>
                <h4 class="font-bold text-dark">首次联系话术模板</h4>
                <p class="text-gray-600 text-sm mt-1">包含专业的首次电话联系和邮件沟通的话术模板，帮助销售代表建立良好的第一印象。</p>
                <a href="#" class="text-primary text-sm mt-2 inline-block hover:underline">
                  查看模板 <i class="fa fa-external-link ml-1"></i>
                </a>
              </div>
            </div>
            
            <div class="border rounded-lg p-4 flex items-start hover:bg-gray-50 transition-colors">
              <div class="text-primary text-xl mt-1 mr-4">
                <i class="fa fa-file-text-o"></i>
              </div>
              <div>
                <h4 class="font-bold text-dark">内容培育序列规划</h4>
                <p class="text-gray-600 text-sm mt-1">为低质量线索设计的内容培育计划模板，包括内容类型、发送频率和目标。</p>
                <a href="#" class="text-primary text-sm mt-2 inline-block hover:underline">
                  下载模板 <i class="fa fa-download ml-1"></i>
                </a>
              </div>
            </div>
            
            <div class="border rounded-lg p-4 flex items-start hover:bg-gray-50 transition-colors">
              <div class="text-primary text-xl mt-1 mr-4">
                <i class="fa fa-video-camera"></i>
              </div>
              <div>
                <h4 class="font-bold text-dark">线索获取培训视频</h4>
                <p class="text-gray-600 text-sm mt-1">关于如何有效获取和筛选高质量线索的培训视频，时长约20分钟。</p>
                <a href="#" class="text-primary text-sm mt-2 inline-block hover:underline">
                  观看视频 <i class="fa fa-play-circle ml-1"></i>
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 其他阶段内容（默认隐藏） -->
      <div class="stage-content hidden" id="stage-2-content">
        <!-- 初步接触阶段内容 -->
        <div class="bg-white rounded-xl shadow-card p-6">
          <h3 class="text-xl font-bold text-dark mb-6">初步接触阶段流程</h3>
          <p class="text-gray-600 mb-6">这个阶段的主要目标是通过首次电话或其他渠道与潜在客户建立联系，建立信任，获取联系方式，为后续跟进打下基础。</p>
          
          <!-- 此处省略详细内容，实际应用中会与线索获取阶段类似的结构 -->
          <div class="text-center py-12">
            <p class="text-gray-500">点击上方标签查看其他阶段内容</p>
          </div>
        </div>
      </div>
      
      <!-- 其他阶段内容省略... -->
    </div>
  </section>

  <!-- 销售资源 -->
  <section id="resources" class="py-16 bg-white">
    <div class="container mx-auto px-4">
      <div class="text-center mb-12">
        <h2 class="text-[clamp(1.5rem,3vw,2.5rem)] font-bold text-dark mb-4">销售资源中心</h2>
        <p class="text-gray-600 max-w-2xl mx-auto">为您提供全面的销售工具、模板和培训资源，助力销售团队高效执行流程。</p>
      </div>
      
      <!-- 资源分类 -->
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
        <div class="bg-primary/5 rounded-xl p-6 hover:shadow-lg transition-shadow cursor-pointer">
          <div class="w-14 h-14 bg-primary/10 rounded-full flex items-center justify-center text-primary text-2xl mb-4">
            <i class="fa fa-file-text-o"></i>
          </div>
          <h3 class="font-bold text-dark text-lg mb-3">销售模板</h3>
          <p class="text-gray-600">包含各种销售场景的专业话术、邮件模板、提案模板等，帮助您高效沟通。</p>
          <a href="#" class="text-primary font-medium mt-4 inline-block hover:underline">
            查看全部模板 <i class="fa fa-arrow-right ml-1"></i>
          </a>
        </div>
        
        <div class="bg-primary/5 rounded-xl p-6 hover:shadow-lg transition-shadow cursor-pointer">
          <div class="w-14 h-14 bg-primary/10 rounded-full flex items-center justify-center text-primary text-2xl mb-4">
            <i class="fa fa-book"></i>
          </div>
          <h3 class="font-bold text-dark text-lg mb-3">培训资料</h3>
          <p class="text-gray-600">提供系统的销售技能培训材料、产品知识和行业洞察，提升团队专业能力。</p>
          <a href="#" class="text-primary font-medium mt-4 inline-block hover:underline">
            浏览培训资料 <i class="fa fa-arrow-right ml-1"></i>
          </a>
        </div>
        
        <div class="bg-primary/5 rounded-xl p-6 hover:shadow-lg transition-shadow cursor-pointer">
          <div class="w-14 h-14 bg-primary/10 rounded-full flex items-center justify-center text-primary text-2xl mb-4">
            <i class="fa fa-line-chart"></i>
          </div>
          <h3 class="font-bold text-dark text-lg mb-3">数据分析工具</h3>
          <p class="text-gray-600">帮助您跟踪销售数据、分析销售漏斗、优化销售策略的工具和指南。</p>
          <a href="#" class="text-primary font-medium mt-4 inline-block hover:underline">
            探索数据分析 <i class="fa fa-arrow-right ml-1"></i>
          </a>
        </div>
      </div>
      
      <!-- 精选资源 -->
      <h3 class="text-xl font-bold text-dark mb-6">精选资源</h3>
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div class="bg-white rounded-xl shadow-card overflow-hidden hover:shadow-card-hover transition-shadow">
          <div class="h-40 bg-primary/20 relative">
            <img src="https://picsum.photos/seed/sales1/400/200" alt="销售话术模板" class="w-full h-full object-cover">
            <div class="absolute top-3 left-3 bg-primary text-white text-xs font-medium px-2 py-1 rounded">
              模板
            </div>
          </div>
          <div class="p-5">
            <h4 class="font-bold text-dark mb-2">销售话术模板库</h4>
            <p class="text-gray-600 text-sm mb-4">包含首次接触、需求挖掘、异议处理等各阶段的专业话术模板。</p>
            <div class="flex items-center justify-between">
              <span class="text-xs text-gray-500">更新于 2025-07-10</span>
              <a href="#" class="text-primary text-sm hover:underline">下载</a>
            </div>
          </div>
        </div>
        
        <div class="bg-white rounded-xl shadow-card overflow-hidden hover:shadow-card-hover transition-shadow">
          <div class="h-40 bg-primary/20 relative">
            <img src="https://picsum.photos/seed/sales2/400/200" alt="需求挖掘指南" class="w-full h-full object-cover">
            <div class="absolute top-3 left-3 bg-secondary text-white text-xs font-medium px-2 py-1 rounded">
              指南
            </div>
          </div>
          <div class="p-5">
            <h4 class="font-bold text-dark mb-2">5W2H需求挖掘指南</h4>
            <p class="text-gray-600 text-sm mb-4">详细介绍如何使用5W2H方法论深入挖掘客户需求的实用指南。</p>
            <div class="flex items-center justify-between">
              <span class="text-xs text-gray-500">更新于 2025-06-25</span>
              <a href="#" class="text-primary text-sm hover:underline">查看</a>
            </div>
          </div>
        </div>
        
        <div class="bg-white rounded-xl shadow-card overflow-hidden hover:shadow-card-hover transition-shadow">
          <div class="h-40 bg-primary/20 relative">
            <img src="https://picsum.photos/seed/sales3/400/200" alt="异议处理培训" class="w-full h-full object-cover">
            <div class="absolute top-3 left-3 bg-success text-white text-xs font-medium px-2 py-1 rounded">
              视频
            </div>
          </div>
          <div class="p-5">
            <h4 class="font-bold text-dark mb-2">异议处理技巧培训</h4>
            <p class="text-gray-600 text-sm mb-4">60分钟视频培训，讲解如何有效识别和处理客户异议的实用技巧。</p>
            <div class="flex items-center justify-between">
              <span class="text-xs text-gray-500">更新于 2025-07-05</span>
              <a href="#" class="text-primary text-sm hover:underline">观看</a>
            </div>
          </div>
        </div>
        
        <div class="bg-white rounded-xl shadow-card overflow-hidden hover:shadow-card-hover transition-shadow">
          <div class="h-40 bg-primary/20 relative">
            <img src="https://picsum.photos/seed/sales4/400/200" alt="销售数据分析" class="w-full h-full object-cover">
            <div class="absolute top-3 left-3 bg-warning text-white text-xs font-medium px-2 py-1 rounded">
              工具
            </div>
          </div>
          <div class="p-5">
            <h4 class="font-bold text-dark mb-2">销售漏斗分析工具</h4>
            <p class="text-gray-600 text-sm mb-4">Excel模板，帮助您分析销售漏斗各阶段转化率和关键指标。</p>
            <div class="flex items-center justify-between">
              <span class="text-xs text-gray-500">更新于 2025-07-01</span>
              <a href="#" class="text-primary text-sm hover:underline">下载</a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- 联系我们 -->
  <section id="contact" class="py-16 bg-gray-50">
    <div class="container mx-auto px-4">
      <div class="max-w-4xl mx-auto">
        <div class="text-center mb-12">
          <h2 class="text-[clamp(1.5rem,3vw,2.5rem)] font-bold text-dark mb-4">联系我们</h2>
          <p class="text-gray-600 max-w-2xl mx-auto">有任何问题或需要进一步的支持？请随时联系我们的销售支持团队。</p>
        </div>
        
        <div class="bg-white rounded-xl shadow-card p-6 md:p-8">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div>
              <h3 class="font-bold text-dark text-lg mb-4">销售支持团队</h3>
              <p class="text-gray-600 mb-6">我们的专业销售支持团队随时为您提供帮助，解答疑问，提供培训和资源。</p>
              
              <div class="space-y-4">
                <div class="flex items-start">
                  <div class="text-primary text-xl mt-1 mr-4">
                    <i class="fa fa-envelope-o"></i>
                  </div>
                  <div>
                    <h4 class="font-medium text-dark">电子邮件</h4>
                    <p class="text-gray-600"><EMAIL></p>
                  </div>
                </div>
                
                <div class="flex items-start">
                  <div class="text-primary text-xl mt-1 mr-4">
                    <i class="fa fa-phone"></i>
                  </div>
                  <div>
                    <h4 class="font-medium text-dark">电话</h4>
                    <p class="text-gray-600">************</p>
                  </div>
                </div>
                
                <div class="flex items-start">
                  <div class="text-primary text-xl mt-1 mr-4">
                    <i class="fa fa-clock-o"></i>
                  </div>
                  <div>
                    <h4 class="font-medium text-dark">工作时间</h4>
                    <p class="text-gray-600">周一至周五: 9:00 - 18:00</p>
                  </div>
                </div>
              </div>
              
              <div class="mt-8">
                <h4 class="font-bold text-dark mb-3">关注我们</h4>
                <div class="flex space-x-4">
                  <a href="#" class="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center text-primary hover:bg-primary hover:text-white transition-colors">
                    <i class="fa fa-weixin"></i>
                  </a>
                  <a href="#" class="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center text-primary hover:bg-primary hover:text-white transition-colors">
                    <i class="fa fa-weibo"></i>
                  </a>
                  <a href="#" class="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center text-primary hover:bg-primary hover:text-white transition-colors">
                    <i class="fa fa-linkedin"></i>
                  </a>
                </div>
              </div>
            </div>
            
            <div>
              <h3 class="font-bold text-dark text-lg mb-4">发送消息</h3>
              <form>
                <div class="mb-4">
                  <label for="name" class="block text-sm font-medium text-gray-700 mb-1">姓名</label>
                  <input type="text" id="name" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary transition-colors" placeholder="请输入您的姓名">
                </div>
                
                <div class="mb-4">
                  <label for="email" class="block text-sm font-medium text-gray-700 mb-1">邮箱</label>
                  <input type="email" id="email" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary transition-colors" placeholder="请输入您的邮箱">
                </div>
                
                <div class="mb-4">
                  <label for="subject" class="block text-sm font-medium text-gray-700 mb-1">主题</label>
                  <select id="subject" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary transition-colors">
                    <option value="">请选择咨询主题</option>
                    <option value="training">销售培训</option>
                    <option value="resources">资源获取</option>
                    <option value="process">流程咨询</option>
                    <option value="other">其他问题</option>
                  </select>
                </div>
                
                <div class="mb-6">
                  <label for="message" class="block text-sm font-medium text-gray-700 mb-1">消息内容</label>
                  <textarea id="message" rows="4" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary transition-colors" placeholder="请输入您的消息内容"></textarea>
                </div>
                
                <button type="submit" class="w-full bg-primary text-white font-medium py-3 px-4 rounded-lg hover:bg-primary/90 transition-colors shadow-md hover:shadow-lg transform hover:-translate-y-0.5 duration-300">
                  发送消息 <i class="fa fa-paper-plane ml-2"></i>
                </button>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- 页脚 -->
  <footer class="bg-dark text-white py-12">
    <div class="container mx-auto px-4">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
        <div>
          <h3 class="text-xl font-bold mb-4 flex items-center">
            <i class="fa fa-line-chart mr-3"></i>
            鑫淼·翼企办
          </h3>
          <p class="text-gray-400 mb-4">专业的企业服务平台，为企业提供全方位的数字化解决方案。</p>
          <div class="flex space-x-4">
            <a href="#" class="text-gray-400 hover:text-white transition-colors">
              <i class="fa fa-weixin"></i>
            </a>
            <a href="#" class="text-gray-400 hover:text-white transition-colors">
              <i class="fa fa-weibo"></i>
            </a>
            <a href="#" class="text-gray-400 hover:text-white transition-colors">
              <i class="fa fa-linkedin"></i>
            </a>
            <a href="#" class="text-gray-400 hover:text-white transition-colors">
              <i class="fa fa-youtube-play"></i>
            </a>
          </div>
        </div>
        
        <div>
          <h4 class="font-bold mb-4">快速链接</h4>
          <ul class="space-y-2">
            <li><a href="#overview" class="text-gray-400 hover:text-white transition-colors">流程概览</a></li>
            <li><a href="#stages" class="text-gray-400 hover:text-white transition-colors">详细阶段</a></li>
            <li><a href="#resources" class="text-gray-400 hover:text-white transition-colors">销售资源</a></li>
            <li><a href="#contact" class="text-gray-400 hover:text-white transition-colors">联系我们</a></li>
          </ul>
        </div>
        
        <div>
          <h4 class="font-bold mb-4">解决方案</h4>
          <ul class="space-y-2">
            <li><a href="#" class="text-gray-400 hover:text-white transition-colors">数字化转型</a></li>
            <li><a href="#" class="text-gray-400 hover:text-white transition-colors">客户关系管理</a></li>
            <li><a href="#" class="text-gray-400 hover:text-white transition-colors">销售自动化</a></li>
            <li><a href="#" class="text-gray-400 hover:text-white transition-colors">数据分析</a></li>
          </ul>
        </div>
        
        <div>
          <h4 class="font-bold mb-4">联系信息</h4>
          <ul class="space-y-2">
            <li class="flex items-start">
              <i class="fa fa-map-marker text-primary mt-1 mr-3"></i>
              <span class="text-gray-400">北京市海淀区中关村科技园区8号楼</span>
            </li>
            <li class="flex items-start">
              <i class="fa fa-phone text-primary mt-1 mr-3"></i>
              <span class="text-gray-400">************</span>
            </li>
            <li class="flex items-start">
              <i class="fa fa-envelope-o text-primary mt-1 mr-3"></i>
              <span class="text-gray-400"><EMAIL></span>
            </li>
          </ul>
        </div>
      </div>
      
      <div class="border-t border-gray-800 mt-10 pt-6 flex flex-col md:flex-row justify-between items-center">
        <p class="text-gray-500 text-sm">© 2025 鑫淼·翼企办. 保留所有权利.</p>
        <div class="flex space-x-6 mt-4 md:mt-0">
          <a href="#" class="text-gray-500 hover:text-gray-300 text-sm">隐私政策</a>
          <a href="#" class="text-gray-500 hover:text-gray-300 text-sm">服务条款</a>
          <a href="#" class="text-gray-500 hover:text-gray-300 text-sm">法律声明</a>
        </div>
      </div>
    </div>
  </footer>

  <!-- JavaScript -->
  <script>
    // 初始化所有交互功能
    document.addEventListener('DOMContentLoaded', function() {
      // 阶段标签切换
      initStageTabs();
      // 步骤卡片动画
      initStepCards();
      // 进度条动画
      initProgressLines();
      // 异议处理展示
      initObjectionHandling();
    });

    // 阶段标签切换功能
    function initStageTabs() {
      const stageTabs = document.querySelectorAll('.stage-tab');
      const stageContents = document.querySelectorAll('.stage-content');
      
      stageTabs.forEach(tab => {
        tab.addEventListener('click', () => {
          // 移除所有标签的活动状态
          stageTabs.forEach(t => {
            t.classList.remove('active', 'bg-primary', 'text-white');
            t.classList.add('bg-gray-200', 'text-gray-700');
          });
          
          // 添加当前标签的活动状态
          tab.classList.add('active', 'bg-primary', 'text-white');
          tab.classList.remove('bg-gray-200', 'text-gray-700');
          
          // 隐藏所有内容
          stageContents.forEach(content => {
            content.classList.add('hidden');
          });
          
          // 显示对应内容
          const stageId = tab.getAttribute('data-stage');
          const activeContent = document.getElementById(`stage-${stageId}-content`);
          activeContent.classList.remove('hidden');
        });
      });
      
      // 平滑滚动
      document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
          e.preventDefault();
          
          document.querySelector(this.getAttribute('href')).scrollIntoView({
            behavior: 'smooth'
          });
        });
      });
    });

    // 步骤卡片动画功能
    function initStepCards() {
      const stepCards = document.querySelectorAll('.step-card');
      
      stepCards.forEach(card => {
        card.addEventListener('mouseenter', () => {
          card.classList.add('shadow-lg');
          card.classList.add('scale-105');
        });
        
        card.addEventListener('mouseleave', () => {
          card.classList.remove('shadow-lg');
          card.classList.remove('scale-105');
        });
      });
    });
  </script>
</body>
</html>
    