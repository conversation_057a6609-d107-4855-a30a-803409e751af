// 阶段详情数据
const stageDetails = {
    1: {
        title: '线索获取与电话初筛阶段',
        content: {
            keyPoints: [
                '电话开场白标准化',
                'BANT+模型详细评分标准',
                '行业特定问题库',
                '常见异议处理话术'
            ],
            tools: [
                {
                    name: 'BANT+评分表',
                    description: '预算(Budget)、权限(Authority)、需求(Need)、时间(Timing)加强版评分体系'
                },
                {
                    name: '行业话术库',
                    description: '针对不同行业的标准开场白和问题库'
                },
                {
                    name: '微信获取话术',
                    description: '自然引导获取微信的标准话术'
                }
            ],
            bestPractices: [
                '首句话抓住客户注意力',
                '控制通话时间在3-5分钟内',
                '设置下一步沟通预期',
                '自然引导转到微信沟通'
            ]
        }
    },
    2: {
        title: '微信沟通与需求挖掘阶段',
        content: {
            keyPoints: [
                '建立信任关系',
                '需求深度挖掘',
                '价值内容分享',
                '互动频率把控'
            ],
            tools: [
                {
                    name: '5W2H分析框架',
                    description: 'What、Why、Who、When、Where、How、How much全方位需求分析'
                },
                {
                    name: '行业案例库',
                    description: '分行业、分场景的成功案例库'
                },
                {
                    name: '需求确认单',
                    description: '标准化的需求记录和确认文档'
                }
            ],
            bestPractices: [
                '首次互动要在10分钟内',
                '建立朋友式的沟通氛围',
                '通过案例引导需求',
                '及时总结确认需求点'
            ]
        }
    },
    // ... 其他阶段详情
};

// 初始化页面
document.addEventListener('DOMContentLoaded', () => {
    initializeStageCards();
    initializeToolbar();
});

// 初始化阶段卡片
function initializeStageCards() {
    const cards = document.querySelectorAll('.stage-card');
    cards.forEach(card => {
        card.querySelector('.expand-btn').addEventListener('click', () => {
            const stageNumber = card.dataset.stage;
            showStageDetail(stageNumber);
        });
    });
}

// 初始化工具栏
function initializeToolbar() {
    const toolbarItems = document.querySelectorAll('.toolbar-item');
    toolbarItems.forEach(item => {
        item.addEventListener('click', () => {
            const feature = item.dataset.feature;
            handleToolbarClick(feature);
        });
    });
}

// 显示阶段详情
function showStageDetail(stageNumber) {
    const modal = document.getElementById('stageDetailModal');
    const title = document.getElementById('stageDetailTitle');
    const content = document.getElementById('stageDetailContent');
    
    const detail = stageDetails[stageNumber];
    if (!detail) return;

    title.textContent = detail.title;
    
    content.innerHTML = generateDetailContent(detail.content);
    
    modal.style.display = 'block';
}

// 生成详情内容HTML
function generateDetailContent(content) {
    return `
        <div class="detail-section">
            <h3>关键要点</h3>
            <ul>
                ${content.keyPoints.map(point => `<li>${point}</li>`).join('')}
            </ul>
        </div>
        <div class="detail-section">
            <h3>工具与模板</h3>
            <div class="tools-grid">
                ${content.tools.map(tool => `
                    <div class="tool-card">
                        <h4>${tool.name}</h4>
                        <p>${tool.description}</p>
                    </div>
                `).join('')}
            </div>
        </div>
        <div class="detail-section">
            <h3>最佳实践</h3>
            <ul>
                ${content.bestPractices.map(practice => `<li>${practice}</li>`).join('')}
            </ul>
        </div>
    `;
}

// 处理工具栏点击
function handleToolbarClick(feature) {
    switch(feature) {
        case 'competitive':
            showCompetitiveDialog();
            break;
        case 'training':
            showTrainingModules();
            break;
        case 'tools':
            showSalesTools();
            break;
        case 'skills':
            showSalesSkills();
            break;
    }
}

// 关闭模态框
function closeStageDetail() {
    document.getElementById('stageDetailModal').style.display = 'none';
}

// 关闭业绩追踪模态框
function closePerformanceModal() {
    document.getElementById('performanceModal').style.display = 'none';
}

// 其他功能模块
function showCompetitiveDialog() {
    // 实现竞品话术功能
}

function showTrainingModules() {
    // 实现培训模块功能
}

function showSalesTools() {
    // 实现销售工具功能
}

function showSalesSkills() {
    // 实现销售技巧功能
}

// 添加ESC键关闭模态框
document.addEventListener('keydown', (e) => {
    if (e.key === 'Escape') {
        closeStageDetail();
        closePerformanceModal();
    }
});

// 点击模态框外部关闭
window.addEventListener('click', (e) => {
    const stageModal = document.getElementById('stageDetailModal');
    const performanceModal = document.getElementById('performanceModal');
    
    if (e.target === stageModal) {
        closeStageDetail();
    }
    if (e.target === performanceModal) {
        closePerformanceModal();
    }
});
