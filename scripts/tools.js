// 销售工具模块
export const salesTools = {
    需求分析: {
        'BANT+评分表': {
            description: '线索质量评估工具',
            usage: [
                '预算评估（1-5分）',
                '决策权评估（1-5分）',
                '需求程度评估（1-5分）',
                '时间紧迫度评估（1-5分）',
                '附加项：竞品考察情况（1-5分）'
            ],
            template: {
                budget: {
                    questions: [
                        '是否有明确的预算？',
                        '预算区间是多少？',
                        '预算审批流程如何？'
                    ],
                    scoring: '根据答案明确程度和预算金额评分'
                },
                authority: {
                    questions: [
                        '接触人职级如何？',
                        '是否是决策人？',
                        '决策链条涉及哪些角色？'
                    ],
                    scoring: '根据决策影响力评分'
                }
            }
        },
        '5W2H分析表': {
            description: '深度需求分析工具',
            usage: [
                'What - 要解决什么问题？',
                'Why - 为什么要解决这个问题？',
                'Who - 谁来使用解决方案？',
                'When - 什么时候需要？',
                'Where - 在哪里使用？',
                'How - 如何实施？',
                'How much - 投入多少资源？'
            ],
            template: {
                sections: [
                    '问题描述',
                    '原因分析',
                    '用户分析',
                    '时间规划',
                    '使用场景',
                    '实施方案',
                    '资源预算'
                ]
            }
        }
    },
    方案设计: {
        '解决方案模板': {
            description: '标准化方案设计工具',
            sections: [
                '客户背景分析',
                '现状问题描述',
                '解决方案详情',
                '实施计划',
                '投资回报分析',
                '客户价值呈现'
            ]
        },
        'ROI计算器': {
            description: '投资回报计算工具',
            metrics: [
                '成本节约',
                '效率提升',
                '收入增长',
                '风险规避'
            ]
        }
    },
    商务谈判: {
        '价格谈判指南': {
            description: '价格谈判策略工具',
            strategies: [
                '价值锚定',
                '让步空间设计',
                '打包定价策略',
                '分期付款方案'
            ]
        },
        '合同模板': {
            description: '标准化合同模板',
            sections: [
                '服务内容',
                '计费方式',
                '服务级别',
                '交付标准'
            ]
        }
    }
};

export function generateProposal(clientInfo, requirements) {
    // 根据客户信息和需求生成建议书
}

export function calculateROI(parameters) {
    // 计算投资回报
}

export function createContract(clientInfo, dealTerms) {
    // 生成合同
}
