// 竞品话术模块
export function showCompetitiveDialog() {
    const content = {
        主要竞品: {
            '竞品A': {
                优势应对: [
                    '我们的产品具有更高的性价比',
                    '我们的服务更加本地化',
                    '我们的技术架构更先进'
                ],
                典型问题: [
                    'Q: 他们价格更便宜怎么办？',
                    'A: 我们提供更多的增值服务，总拥有成本更低',
                    'Q: 他们功能更多怎么办？',
                    'A: 我们的核心功能更专注，更符合客户实际需求'
                ]
            },
            '竞品B': {
                优势应对: [
                    '我们的产品更易用',
                    '我们的升级更频繁',
                    '我们的集成更方便'
                ],
                典型问题: [
                    'Q: 他们知名度更高怎么办？',
                    'A: 我们更注重实际效果，有具体的案例支撑',
                    'Q: 他们历史更久怎么办？',
                    'A: 我们的技术更新，没有历史包袱'
                ]
            }
        },
        行业特点: {
            '制造业': [
                '强调效率提升',
                '突出成本节约',
                '强调实施速度'
            ],
            '服务业': [
                '突出客户体验',
                '强调服务质量',
                '突出灵活性'
            ],
            '零售业': [
                '强调营收提升',
                '突出库存优化',
                '强调多渠道整合'
            ]
        }
    };
    
    // 实现竞品话术展示逻辑
    return content;
}
