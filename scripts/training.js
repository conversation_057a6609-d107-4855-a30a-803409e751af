// 培训模块内容
export const trainingModules = {
    基础培训: {
        '销售流程': {
            课程内容: [
                '7阶段销售流程详解',
                '各阶段关键点和注意事项',
                '阶段之间的转换技巧'
            ],
            考核标准: [
                '能完整描述7个阶段的核心工作',
                '能正确处理各阶段常见问题',
                '能灵活运用阶段转换技巧'
            ]
        },
        '产品知识': {
            课程内容: [
                '产品核心功能详解',
                '产品技术架构介绍',
                '产品优势分析'
            ],
            考核标准: [
                '能准确描述产品核心功能',
                '能结合客户场景推荐合适功能',
                '能有效应对产品相关疑问'
            ]
        },
        '行业知识': {
            课程内容: [
                '重点行业分析',
                '行业痛点解析',
                '解决方案匹配'
            ],
            考核标准: [
                '能分析行业特征和趋势',
                '能识别行业典型痛点',
                '能制定针对性解决方案'
            ]
        }
    },
    进阶培训: {
        '谈判技巧': {
            课程内容: [
                '商务谈判策略',
                '价格谈判技巧',
                '异议处理方法'
            ],
            考核标准: [
                '能制定有效的谈判策略',
                '能灵活运用谈判技巧',
                '能成功处理价格异议'
            ]
        },
        '方案设计': {
            课程内容: [
                '需求分析方法',
                '方案设计原则',
                '方案呈现技巧'
            ],
            考核标准: [
                '能完成专业的需求分析',
                '能设计可行的解决方案',
                '能进行有效的方案呈现'
            ]
        },
        '客户管理': {
            课程内容: [
                '客户关系维护',
                '客户价值提升',
                '客户忠诚度建设'
            ],
            考核标准: [
                '能建立良好的客户关系',
                '能持续提升客户价值',
                '能提高客户忠诚度'
            ]
        }
    }
};

// 培训进度追踪
export const trainingProgress = {
    trackProgress(userId, moduleId) {
        // 实现培训进度追踪逻辑
    },
    
    completeModule(userId, moduleId) {
        // 实现模块完成逻辑
    },
    
    getProgress(userId) {
        // 获取培训进度
    }
};
