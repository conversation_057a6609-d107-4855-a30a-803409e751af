# 翼企办销售指南网站 AI 助手指南

本项目是一个基于 HTML、TailwindCSS 和原生 JavaScript 构建的销售指南网站。以下内容将帮助你快速理解项目结构和关键开发要点。

## 项目概述

这是一个面向销售团队的指南网站，包含:
- 销售流程导航
- 产品知识库
- 交付指南
- AI 配置指南
- 资源中心

## 项目架构

```
/
├── 交付指南.html      # 产品交付和使用指南
├── 价值销售指南.html   # 销售价值主张指南
├── xiaoshou.html     # 销售流程导航页面
├── *.png            # 图片资源
```

## 技术栈

- HTML5
- TailwindCSS - 样式系统
- Font Awesome - 图标库
- 原生 JavaScript - 交互逻辑

## 关键开发规范

### 样式开发
1. 使用 TailwindCSS 实现样式，关键色值:
```js 
{
    primary: '#165DFF',  // 主色
    secondary: '#0FC6C2',
    success: '#00B42A',
    warning: '#FF7D00',
    danger: '#F53F3F',
    dark: '#1D2129'
}
```

2. 通用样式类:
- `.card-hover` - 卡片悬停效果
- `.section-fade-in` - 区块淡入动画
- `.step-card` - 步骤卡片样式

### 交互实现
1. 导航交互:
- 滚动时导航栏样式变化
- 点击导航项平滑滚动
- 移动端菜单展开/收起

2. 内容展示:
- 阶段标签切换
- 卡片悬停动画
- 滚动时内容淡入

### HTML 结构规范
1. 语义化标签使用:
- `<section>` 用于主要内容区块
- `<header>` 用于页面头部
- `<footer>` 用于页面底部
- `<nav>` 用于导航菜单

2. 响应式布局:
- 使用 Tailwind 的响应式类(md:, lg:)
- 移动端优先设计
- 灵活使用 Grid 和 Flex 布局

### 链接管理
所有外部资源链接(飞书文档等)统一在 HTML 文件中管理，便于维护和更新。

### 性能优化
1. 图片资源:
- 使用适当的图片格式和大小
- 添加合适的 alt 属性

2. JavaScript:
- 事件委托处理类似交互
- 使用 IntersectionObserver 实现滚动动画
- DOM 操作最小化

## 常见开发任务

### 1. 添加新的内容板块

```html
<section id="new-section" class="py-16 bg-white section-fade-in">
    <div class="container mx-auto px-4">
        <div class="text-center mb-12">
            <h2 class="text-[clamp(1.5rem,3vw,2.5rem)] font-bold text-dark mb-4">
                标题
            </h2>
            <p class="text-gray-600 max-w-2xl mx-auto">描述文本</p>
        </div>
        <!-- 内容 -->
    </div>
</section>
```

### 2. 添加新的卡片组件

```html
<div class="bg-white rounded-xl shadow-card p-6 hover:shadow-card-hover transition-shadow">
    <div class="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center text-primary text-xl mb-4">
        <i class="fa fa-[icon-name]"></i>
    </div>
    <h3 class="font-bold text-dark mb-3">标题</h3>
    <p class="text-gray-600">描述内容</p>
</div>
```

### 3. 添加新的流程步骤

```html
<div class="step-card step-active rounded-lg border p-4">
    <div class="flex items-center">
        <div class="step-icon border-2">步骤数字</div>
        <h4 class="font-bold text-dark ml-4">步骤名称</h4>
    </div>
    <p class="text-gray-600 mt-3">步骤描述</p>
</div>
```

## 提示与建议

1. 保持代码风格统一：
- 使用现有的样式类而不是创建新的
- 遵循已有的命名约定
- 保持 HTML 结构的一致性

2. 响应式开发：
- 始终考虑移动端显示效果
- 使用 Tailwind 的响应式工具类
- 测试不同屏幕尺寸下的显示

3. 内容更新：
- 确保新增内容符合整体风格
- 保持导航和链接的同步更新
- 维护文档的完整性和一致性
