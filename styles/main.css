/* 全局样式 */
:root {
    --primary-color: #165DFF;
    --secondary-color: #0FC6C2;
    --success-color: #00B42A;
    --warning-color: #FF7D00;
    --danger-color: #F53F3F;
    --bg-light: #F5F7FA;
    --text-dark: #1D2129;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    line-height: 1.6;
    color: var(--text-dark);
    background: var(--bg-light);
    margin: 0;
    padding: 0;
}

/* 工具栏样式 */
.toolbar {
    position: fixed;
    top: 20px;
    right: 20px;
    display: flex;
    flex-direction: column;
    gap: 10px;
    z-index: 100;
}

.toolbar-item {
    background: white;
    padding: 12px 20px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.toolbar-item:hover {
    transform: translateX(-5px);
    box-shadow: 2px 4px 12px rgba(0,0,0,0.15);
}

/* 容器样式 */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 80px 20px 40px;
}

.header {
    text-align: center;
    margin-bottom: 60px;
}

.header h1 {
    font-size: 2.5rem;
    color: var(--text-dark);
    margin-bottom: 16px;
}

.core-concept {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    padding: 16px;
    border-radius: 8px;
    margin-top: 24px;
    font-size: 1.1rem;
}

/* 流程卡片样式 */
.flow-container {
    display: grid;
    gap: 30px;
}

.stage-card {
    background: white;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.08);
    transition: all 0.3s ease;
}

.stage-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 24px rgba(0,0,0,0.12);
}

.stage-header {
    display: flex;
    align-items: center;
    gap: 16px;
    margin-bottom: 16px;
}

.stage-number {
    width: 40px;
    height: 40px;
    background: var(--primary-color);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 1.2rem;
}

.stage-title {
    font-size: 1.3rem;
    font-weight: bold;
    color: var(--text-dark);
}

.stage-description {
    color: #666;
    margin-bottom: 16px;
}

.stage-target {
    background: var(--bg-light);
    padding: 12px;
    border-radius: 6px;
    margin-bottom: 20px;
}

.flow-steps {
    display: grid;
    gap: 12px;
}

.flow-step {
    display: flex;
    align-items: center;
    gap: 12px;
}

.flow-step-icon {
    width: 24px;
    height: 24px;
    border: 2px solid var(--primary-color);
    border-radius: 50%;
    position: relative;
}

.flow-step-icon::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 8px;
    height: 8px;
    background: var(--primary-color);
    border-radius: 50%;
    transform: translate(-50%, -50%);
}

.expand-btn {
    width: 100%;
    padding: 12px;
    margin-top: 20px;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.expand-btn:hover {
    background: var(--secondary-color);
}

/* 模态框样式 */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    z-index: 1000;
}

.modal-content {
    background: white;
    width: 90%;
    max-width: 800px;
    margin: 40px auto;
    border-radius: 12px;
    position: relative;
    padding: 24px;
}

.close-btn {
    position: absolute;
    top: 16px;
    right: 16px;
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #666;
}

.modal-header {
    margin-bottom: 24px;
}

.modal-title {
    font-size: 1.5rem;
    color: var(--text-dark);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .toolbar {
        position: static;
        flex-direction: row;
        flex-wrap: wrap;
        justify-content: center;
        padding: 20px;
    }

    .container {
        padding-top: 20px;
    }

    .header h1 {
        font-size: 2rem;
    }

    .stage-card {
        padding: 16px;
    }
}
